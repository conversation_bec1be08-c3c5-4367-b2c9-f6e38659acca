"""
Advanced features for the Telegram Music Bot
Includes shuffle, repeat, volume controls, and other enhanced functionality
"""
import logging
import random
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from pyrogram.enums import ParseMode

logger = logging.getLogger("musicbot.advanced_features")

# Global settings storage (in production, this should be in a database)
chat_settings = {}

async def handle_shuffle_queue(bot, callback_query: CallbackQuery):
    """Handle shuffle queue button"""
    try:
        from player.voice_chat import active_chats

        chat_id = callback_query.message.chat.id

        if chat_id not in active_chats or not active_chats[chat_id].get('queue'):
            await callback_query.answer("❌ Queue is empty")
            return

        # Shuffle the queue
        queue = active_chats[chat_id]['queue']
        random.shuffle(queue)
        active_chats[chat_id]['queue'] = queue

        await callback_query.answer("🔀 Queue shuffled!")

        # Refresh the queue view if we're in queue menu
        if "show_queue" in callback_query.message.text or "Queue" in callback_query.message.text:
            from handlers.menu_manager import create_queue_menu
            await create_queue_menu(bot, callback_query.message, chat_id)

    except Exception as e:
        logger.error(f"Error shuffling queue: {e}")
        await callback_query.answer("❌ Error shuffling queue")

async def handle_toggle_repeat(bot, callback_query: CallbackQuery):
    """Handle toggle repeat mode button"""
    try:
        chat_id = callback_query.message.chat.id

        # Get current repeat mode
        if chat_id not in chat_settings:
            chat_settings[chat_id] = {}

        current_mode = chat_settings[chat_id].get('repeat_mode', 'off')

        # Cycle through repeat modes: off -> one -> all -> off
        if current_mode == 'off':
            new_mode = 'one'
            mode_text = "🔂 Repeat: Current Song"
        elif current_mode == 'one':
            new_mode = 'all'
            mode_text = "🔁 Repeat: All Songs"
        else:
            new_mode = 'off'
            mode_text = "➡️ Repeat: Off"

        chat_settings[chat_id]['repeat_mode'] = new_mode
        await callback_query.answer(mode_text)

    except Exception as e:
        logger.error(f"Error toggling repeat: {e}")
        await callback_query.answer("❌ Error changing repeat mode")

async def handle_volume_controls(bot, callback_query: CallbackQuery):
    """Handle volume controls button"""
    try:
        chat_id = callback_query.message.chat.id

        # Get current volume
        if chat_id not in chat_settings:
            chat_settings[chat_id] = {}

        current_volume = chat_settings[chat_id].get('volume', 50)

        volume_text = (
            f"🔊 <b>Volume Control</b>\n\n"
            f"Current Volume: {current_volume}%\n\n"
            f"Use the buttons below to adjust:"
        )

        buttons = [
            [
                InlineKeyboardButton("🔇", callback_data="volume_0"),
                InlineKeyboardButton("🔈", callback_data="volume_25"),
                InlineKeyboardButton("🔉", callback_data="volume_50"),
                InlineKeyboardButton("🔊", callback_data="volume_75"),
                InlineKeyboardButton("📢", callback_data="volume_100")
            ],
            [
                InlineKeyboardButton("➖", callback_data="volume_down"),
                InlineKeyboardButton(f"{current_volume}%", callback_data="volume_current"),
                InlineKeyboardButton("➕", callback_data="volume_up")
            ],
            [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
        ]

        await callback_query.message.edit_text(
            volume_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error showing volume controls: {e}")
        await callback_query.answer("❌ Error loading volume controls")

async def handle_volume_change(bot, callback_query: CallbackQuery, volume_action):
    """Handle volume change actions"""
    try:
        chat_id = callback_query.message.chat.id

        if chat_id not in chat_settings:
            chat_settings[chat_id] = {}

        current_volume = chat_settings[chat_id].get('volume', 50)

        if volume_action.startswith('volume_'):
            if volume_action == 'volume_up':
                new_volume = min(100, current_volume + 10)
            elif volume_action == 'volume_down':
                new_volume = max(0, current_volume - 10)
            elif volume_action == 'volume_current':
                await callback_query.answer(f"Current volume: {current_volume}%")
                return
            else:
                # Direct volume setting (volume_25, volume_50, etc.)
                new_volume = int(volume_action.split('_')[1])
        else:
            return

        chat_settings[chat_id]['volume'] = new_volume

        # Apply volume change to active playback
        from player.voice_chat import active_chats
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            # Note: Actual volume control implementation would depend on the audio library
            # This is a placeholder for the volume change logic
            pass

        await callback_query.answer(f"🔊 Volume set to {new_volume}%")

        # Update the volume controls display
        await handle_volume_controls(bot, callback_query)

    except Exception as e:
        logger.error(f"Error changing volume: {e}")
        await callback_query.answer("❌ Error changing volume")

async def handle_clear_queue(bot, callback_query: CallbackQuery):
    """Optimized clear queue handler"""
    try:
        chat_id = callback_query.message.chat.id

        # Use optimized queue manager
        try:
            from utils.queue_manager import queue_manager
            count = queue_manager.clear_queue(chat_id)

            if count > 0:
                await callback_query.answer(f"🗑️ Cleared {count} songs!")
            else:
                await callback_query.answer("❌ Queue is already empty")
        except ImportError:
            # Fallback to old system
            from player.voice_chat import active_chats

            if chat_id not in active_chats or not active_chats[chat_id].get('queue'):
                await callback_query.answer("❌ Queue is already empty")
                return

            queue_length = len(active_chats[chat_id]['queue'])
            active_chats[chat_id]['queue'] = []
            await callback_query.answer(f"🗑️ Cleared {queue_length} songs!")

        # Refresh the queue view efficiently
        from handlers.menu_manager import create_queue_menu
        await create_queue_menu(bot, callback_query.message, chat_id)

    except Exception as e:
        logger.error(f"Error clearing queue: {e}")
        await callback_query.answer("❌ Error clearing queue")

async def handle_queue_remove(bot, callback_query: CallbackQuery, item_index):
    """Handle removing specific item from queue"""
    try:
        from player.voice_chat import active_chats

        chat_id = callback_query.message.chat.id

        if chat_id not in active_chats or not active_chats[chat_id].get('queue'):
            await callback_query.answer("❌ Queue is empty")
            return

        queue = active_chats[chat_id]['queue']

        # Convert to 0-based index
        index = item_index - 1

        if 0 <= index < len(queue):
            removed_song = queue.pop(index)
            await callback_query.answer(f"❌ Removed: {removed_song['title'][:20]}...")

            # Refresh the queue view
            from handlers.menu_manager import create_queue_menu
            await create_queue_menu(bot, callback_query.message, chat_id)
        else:
            await callback_query.answer("❌ Invalid queue position")

    except Exception as e:
        logger.error(f"Error removing from queue: {e}")
        await callback_query.answer("❌ Error removing song")

async def handle_queue_info(bot, callback_query: CallbackQuery, item_index):
    """Handle showing info for specific queue item"""
    try:
        from player.voice_chat import active_chats

        chat_id = callback_query.message.chat.id

        if chat_id not in active_chats or not active_chats[chat_id].get('queue'):
            await callback_query.answer("❌ Queue is empty")
            return

        queue = active_chats[chat_id]['queue']

        # Convert to 0-based index
        index = item_index - 1

        if 0 <= index < len(queue):
            song = queue[index]
            info_text = f"🎵 {song['title']}\n📍 Position: {item_index}/{len(queue)}"
            await callback_query.answer(info_text, show_alert=True)
        else:
            await callback_query.answer("❌ Invalid queue position")

    except Exception as e:
        logger.error(f"Error showing queue info: {e}")
        await callback_query.answer("❌ Error loading song info")

async def handle_player_settings(bot, callback_query: CallbackQuery):
    """Handle player settings button"""
    try:
        chat_id = callback_query.message.chat.id

        if chat_id not in chat_settings:
            chat_settings[chat_id] = {}

        settings = chat_settings[chat_id]
        repeat_mode = settings.get('repeat_mode', 'off')
        volume = settings.get('volume', 50)
        auto_shuffle = settings.get('auto_shuffle', False)

        # Format repeat mode display
        repeat_display = {
            'off': '➡️ Off',
            'one': '🔂 Current Song',
            'all': '🔁 All Songs'
        }.get(repeat_mode, '➡️ Off')

        settings_text = (
            f"⚙️ <b>Player Settings</b>\n\n"
            f"🔊 Volume: {volume}%\n"
            f"🔁 Repeat: {repeat_display}\n"
            f"🔀 Auto Shuffle: {'✅ On' if auto_shuffle else '❌ Off'}\n\n"
            f"Use the buttons below to adjust settings:"
        )

        buttons = [
            [
                InlineKeyboardButton("🔊 Volume", callback_data="volume_controls"),
                InlineKeyboardButton("🔁 Repeat", callback_data="toggle_repeat")
            ],
            [
                InlineKeyboardButton("🔀 Auto Shuffle", callback_data="toggle_auto_shuffle"),
                InlineKeyboardButton("🎚️ Audio Quality", callback_data="audio_quality")
            ],
            [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
        ]

        await callback_query.message.edit_text(
            settings_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error showing player settings: {e}")
        await callback_query.answer("❌ Error loading settings")

async def handle_toggle_auto_shuffle(bot, callback_query: CallbackQuery):
    """Handle toggle auto shuffle button"""
    try:
        chat_id = callback_query.message.chat.id

        if chat_id not in chat_settings:
            chat_settings[chat_id] = {}

        current_setting = chat_settings[chat_id].get('auto_shuffle', False)
        new_setting = not current_setting

        chat_settings[chat_id]['auto_shuffle'] = new_setting

        status_text = "✅ Auto Shuffle: On" if new_setting else "❌ Auto Shuffle: Off"
        await callback_query.answer(status_text)

        # Refresh the settings view
        await handle_player_settings(bot, callback_query)

    except Exception as e:
        logger.error(f"Error toggling auto shuffle: {e}")
        await callback_query.answer("❌ Error changing auto shuffle")

async def handle_show_stats(bot, callback_query: CallbackQuery):
    """Handle show stats button"""
    try:
        from player.voice_chat import active_chats

        chat_id = callback_query.message.chat.id

        # Calculate stats
        total_chats = len(active_chats)
        current_queue_size = len(active_chats.get(chat_id, {}).get('queue', []))

        # Get current track info
        current_track = None
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            current_track = active_chats[chat_id]['current_track']

        stats_text = (
            f"📊 <b>Music Bot Statistics</b>\n\n"
            f"🎵 Current Track: {current_track['title'] if current_track else 'None'}\n"
            f"📋 Queue Size: {current_queue_size} songs\n"
            f"🏠 Active Chats: {total_chats}\n"
            f"🔊 Volume: {chat_settings.get(chat_id, {}).get('volume', 50)}%\n"
            f"🔁 Repeat Mode: {chat_settings.get(chat_id, {}).get('repeat_mode', 'off').title()}\n"
            f"🔀 Auto Shuffle: {'On' if chat_settings.get(chat_id, {}).get('auto_shuffle', False) else 'Off'}"
        )

        buttons = [
            [
                InlineKeyboardButton("🔄 Refresh", callback_data="show_stats"),
                InlineKeyboardButton("⬅️ Back", callback_data="main_menu")
            ]
        ]

        await callback_query.message.edit_text(
            stats_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error showing stats: {e}")
        await callback_query.answer("❌ Error loading statistics")
