"""
Advanced playlist management for the Telegram Music Bot
"""
import os
import json
import time
import random
import logging
import aiofiles
from typing import List, Dict, Any, Optional, Union

from config import BASE_DIR, MAX_PLAYLIST_SIZE

logger = logging.getLogger("musicbot.playlist")

# Directory for storing playlists
PLAYLISTS_DIR = os.path.join(BASE_DIR, "playlists")
os.makedirs(PLAYLISTS_DIR, exist_ok=True)

class Playlist:
    """Class for managing playlists"""
    
    def __init__(self, name: str, user_id: int):
        self.name = name
        self.user_id = user_id
        self.songs = []
        self.created_at = time.time()
        self.modified_at = time.time()
        self.current_index = 0
        self.repeat_mode = "none"  # none, one, all
        self.is_shuffled = False
        self.original_order = []
        
    @property
    def filename(self) -> str:
        """Get the filename for this playlist"""
        return os.path.join(PLAYLISTS_DIR, f"{self.user_id}_{self.name}.json")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert playlist to dictionary for serialization"""
        return {
            "name": self.name,
            "user_id": self.user_id,
            "songs": self.songs,
            "created_at": self.created_at,
            "modified_at": self.modified_at,
            "current_index": self.current_index,
            "repeat_mode": self.repeat_mode,
            "is_shuffled": self.is_shuffled,
            "original_order": self.original_order
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Playlist':
        """Create a playlist from a dictionary"""
        playlist = cls(data["name"], data["user_id"])
        playlist.songs = data["songs"]
        playlist.created_at = data.get("created_at", time.time())
        playlist.modified_at = data.get("modified_at", time.time())
        playlist.current_index = data.get("current_index", 0)
        playlist.repeat_mode = data.get("repeat_mode", "none")
        playlist.is_shuffled = data.get("is_shuffled", False)
        playlist.original_order = data.get("original_order", [])
        return playlist
    
    async def save(self) -> bool:
        """Save the playlist to disk"""
        try:
            self.modified_at = time.time()
            async with aiofiles.open(self.filename, "w") as f:
                await f.write(json.dumps(self.to_dict(), indent=2))
            return True
        except Exception as e:
            logger.error(f"Error saving playlist {self.name}: {e}")
            return False
    
    async def add_song(self, song: Dict[str, Any]) -> bool:
        """Add a song to the playlist"""
        if len(self.songs) >= MAX_PLAYLIST_SIZE:
            return False
            
        # Store original order if shuffled
        if self.is_shuffled and not self.original_order:
            self.original_order = self.songs.copy()
            
        self.songs.append(song)
        await self.save()
        return True
    
    async def remove_song(self, index: int) -> bool:
        """Remove a song from the playlist"""
        if 0 <= index < len(self.songs):
            # Handle removal in shuffled mode
            if self.is_shuffled and self.original_order:
                # Find the song in original order
                song = self.songs[index]
                if song in self.original_order:
                    self.original_order.remove(song)
            
            # Remove the song
            self.songs.pop(index)
            
            # Adjust current index if needed
            if index < self.current_index:
                self.current_index -= 1
            elif index == self.current_index and self.current_index >= len(self.songs):
                self.current_index = 0
                
            await self.save()
            return True
        return False
    
    async def clear(self) -> bool:
        """Clear all songs from the playlist"""
        self.songs = []
        self.current_index = 0
        self.original_order = []
        await self.save()
        return True
    
    async def shuffle(self) -> bool:
        """Shuffle the playlist"""
        if not self.songs:
            return False
            
        # Save original order if not already shuffled
        if not self.is_shuffled:
            self.original_order = self.songs.copy()
            
        # Get current song
        current_song = self.songs[self.current_index] if 0 <= self.current_index < len(self.songs) else None
        
        # Shuffle the songs
        random.shuffle(self.songs)
        
        # If we had a current song, find its new index
        if current_song:
            try:
                self.current_index = self.songs.index(current_song)
            except ValueError:
                self.current_index = 0
        
        self.is_shuffled = True
        await self.save()
        return True
    
    async def unshuffle(self) -> bool:
        """Restore original playlist order"""
        if not self.is_shuffled or not self.original_order:
            return False
            
        # Get current song
        current_song = self.songs[self.current_index] if 0 <= self.current_index < len(self.songs) else None
        
        # Restore original order
        self.songs = self.original_order.copy()
        self.original_order = []
        
        # If we had a current song, find its new index
        if current_song:
            try:
                self.current_index = self.songs.index(current_song)
            except ValueError:
                self.current_index = 0
        
        self.is_shuffled = False
        await self.save()
        return True
    
    async def set_repeat_mode(self, mode: str) -> bool:
        """Set repeat mode (none, one, all)"""
        if mode not in ["none", "one", "all"]:
            return False
            
        self.repeat_mode = mode
        await self.save()
        return True
    
    def get_current_song(self) -> Optional[Dict[str, Any]]:
        """Get the current song"""
        if not self.songs or self.current_index >= len(self.songs):
            return None
        return self.songs[self.current_index]
    
    async def next_song(self) -> Optional[Dict[str, Any]]:
        """Get the next song based on repeat mode"""
        if not self.songs:
            return None
            
        if self.repeat_mode == "one":
            # Repeat the current song
            return self.get_current_song()
            
        # Move to next song
        if self.repeat_mode == "all":
            # Wrap around to the beginning
            self.current_index = (self.current_index + 1) % len(self.songs)
        else:
            # Move to next song or return None if at the end
            self.current_index += 1
            if self.current_index >= len(self.songs):
                self.current_index = 0
                if self.repeat_mode == "none":
                    return None
        
        await self.save()
        return self.get_current_song()
    
    async def previous_song(self) -> Optional[Dict[str, Any]]:
        """Get the previous song"""
        if not self.songs:
            return None
            
        if self.repeat_mode == "one":
            # Repeat the current song
            return self.get_current_song()
            
        # Move to previous song
        self.current_index = (self.current_index - 1) % len(self.songs)
        await self.save()
        return self.get_current_song()
    
    async def jump_to(self, index: int) -> Optional[Dict[str, Any]]:
        """Jump to a specific song by index"""
        if 0 <= index < len(self.songs):
            self.current_index = index
            await self.save()
            return self.get_current_song()
        return None

async def get_playlist(name: str, user_id: int) -> Optional[Playlist]:
    """Get a playlist by name and user ID"""
    filename = os.path.join(PLAYLISTS_DIR, f"{user_id}_{name}.json")
    
    if not os.path.exists(filename):
        return None
        
    try:
        async with aiofiles.open(filename, "r") as f:
            data = json.loads(await f.read())
        return Playlist.from_dict(data)
    except Exception as e:
        logger.error(f"Error loading playlist {name}: {e}")
        return None

async def create_playlist(name: str, user_id: int) -> Optional[Playlist]:
    """Create a new playlist"""
    # Check if playlist already exists
    existing = await get_playlist(name, user_id)
    if existing:
        return None
        
    playlist = Playlist(name, user_id)
    await playlist.save()
    return playlist

async def delete_playlist(name: str, user_id: int) -> bool:
    """Delete a playlist"""
    filename = os.path.join(PLAYLISTS_DIR, f"{user_id}_{name}.json")
    
    if not os.path.exists(filename):
        return False
        
    try:
        os.remove(filename)
        return True
    except Exception as e:
        logger.error(f"Error deleting playlist {name}: {e}")
        return False

async def list_playlists(user_id: int) -> List[str]:
    """List all playlists for a user"""
    playlists = []
    prefix = f"{user_id}_"
    
    for filename in os.listdir(PLAYLISTS_DIR):
        if filename.startswith(prefix) and filename.endswith(".json"):
            playlist_name = filename[len(prefix):-5]  # Remove prefix and .json
            playlists.append(playlist_name)
            
    return playlists

async def import_playlist(url: str, name: str, user_id: int) -> Optional[Playlist]:
    """Import a playlist from YouTube, Spotify, etc."""
    # This would require integration with external APIs
    # For now, just create an empty playlist
    playlist = await create_playlist(name, user_id)
    return playlist
