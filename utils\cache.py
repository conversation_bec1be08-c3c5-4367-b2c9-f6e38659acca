"""
Advanced caching system for the Telegram Music Bot
Supports both Redis and local file system caching
"""
import os
import json
import time
import hashlib
import logging
from typing import Optional, Dict, Any, Union
import aiofiles
import aiohttp
from config import USE_REDIS_CACHE, REDIS_URL, REDIS_PASSWORD, AUDIO_CACHE_DIR

logger = logging.getLogger("musicbot.cache")

# Initialize Redis if enabled
redis_client = None
if USE_REDIS_CACHE and REDIS_URL:
    try:
        import redis
        redis_client = redis.Redis.from_url(
            REDIS_URL, 
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        logger.info("Redis cache initialized successfully")
    except ImportError:
        logger.warning("Redis package not installed. Using file system cache only.")
    except Exception as e:
        logger.error(f"Failed to initialize Redis: {e}")
        redis_client = None

class Cache:
    """Unified caching system that supports both Redis and file system"""
    
    @staticmethod
    def generate_key(prefix: str, identifier: str) -> str:
        """Generate a cache key from a prefix and identifier"""
        # Create a hash of the identifier to ensure valid filenames
        hashed = hashlib.md5(identifier.encode()).hexdigest()
        return f"{prefix}:{hashed}"
    
    @staticmethod
    async def set(key: str, value: Any, expire: int = 86400) -> bool:
        """
        Store a value in cache
        
        Args:
            key: Cache key
            value: Value to store (will be JSON serialized)
            expire: Expiration time in seconds (default: 24 hours)
            
        Returns:
            bool: Success status
        """
        try:
            # Convert value to JSON string
            json_value = json.dumps(value)
            
            # Try Redis first if available
            if redis_client:
                redis_client.set(key, json_value, ex=expire)
                return True
            
            # Fall back to file system cache
            cache_file = os.path.join(AUDIO_CACHE_DIR, f"{key}.json")
            
            # Store value and expiration time
            cache_data = {
                "value": value,
                "expires_at": time.time() + expire
            }
            
            async with aiofiles.open(cache_file, 'w') as f:
                await f.write(json.dumps(cache_data))
            return True
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    @staticmethod
    async def get(key: str) -> Optional[Any]:
        """
        Retrieve a value from cache
        
        Args:
            key: Cache key
            
        Returns:
            The stored value or None if not found or expired
        """
        try:
            # Try Redis first if available
            if redis_client:
                value = redis_client.get(key)
                if value:
                    return json.loads(value)
            
            # Fall back to file system cache
            cache_file = os.path.join(AUDIO_CACHE_DIR, f"{key}.json")
            
            if not os.path.exists(cache_file):
                return None
                
            async with aiofiles.open(cache_file, 'r') as f:
                content = await f.read()
                
            cache_data = json.loads(content)
            
            # Check if expired
            if cache_data.get("expires_at", 0) < time.time():
                # Remove expired file
                os.remove(cache_file)
                return None
                
            return cache_data.get("value")
            
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    @staticmethod
    async def delete(key: str) -> bool:
        """
        Delete a value from cache
        
        Args:
            key: Cache key
            
        Returns:
            bool: Success status
        """
        try:
            # Try Redis first if available
            if redis_client:
                redis_client.delete(key)
            
            # Also check file system cache
            cache_file = os.path.join(AUDIO_CACHE_DIR, f"{key}.json")
            if os.path.exists(cache_file):
                os.remove(cache_file)
                
            return True
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    @staticmethod
    async def exists(key: str) -> bool:
        """
        Check if a key exists in cache and is not expired
        
        Args:
            key: Cache key
            
        Returns:
            bool: True if key exists and is not expired
        """
        value = await Cache.get(key)
        return value is not None
    
    @staticmethod
    async def clear_all() -> bool:
        """
        Clear all cached data
        
        Returns:
            bool: Success status
        """
        try:
            # Clear Redis if available
            if redis_client:
                redis_client.flushdb()
            
            # Clear file system cache
            for filename in os.listdir(AUDIO_CACHE_DIR):
                if filename.endswith('.json'):
                    os.remove(os.path.join(AUDIO_CACHE_DIR, filename))
                    
            return True
            
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
            return False

# Audio file caching functions
async def cache_audio_metadata(query: str, metadata: Dict[str, Any]) -> bool:
    """Cache audio metadata for a query"""
    key = Cache.generate_key("audio_meta", query)
    return await Cache.set(key, metadata, expire=604800)  # 7 days

async def get_cached_audio_metadata(query: str) -> Optional[Dict[str, Any]]:
    """Get cached audio metadata for a query"""
    key = Cache.generate_key("audio_meta", query)
    return await Cache.get(key)

async def is_audio_file_cached(title: str) -> bool:
    """Check if an audio file is cached on disk"""
    import re
    safe_title = re.sub(r'[^\w\-_\. ]', '_', title)
    audio_path = os.path.join(AUDIO_CACHE_DIR, f"{safe_title}.mp3")
    return os.path.exists(audio_path)

async def get_cached_audio_path(title: str) -> Optional[str]:
    """Get the path to a cached audio file"""
    import re
    safe_title = re.sub(r'[^\w\-_\. ]', '_', title)
    audio_path = os.path.join(AUDIO_CACHE_DIR, f"{safe_title}.mp3")
    
    if os.path.exists(audio_path):
        return audio_path
    return None

# Search results caching
async def cache_search_results(query: str, results: list) -> bool:
    """Cache search results for a query"""
    key = Cache.generate_key("search", query)
    return await Cache.set(key, results, expire=86400)  # 24 hours

async def get_cached_search_results(query: str) -> Optional[list]:
    """Get cached search results for a query"""
    key = Cache.generate_key("search", query)
    return await Cache.get(key)
