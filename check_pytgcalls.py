import inspect
import pytgcalls

# Print the version
print(f"PyTgCalls version: {pytgcalls.__version__}")

# Print the structure
print("\nModule structure:")
for name in dir(pytgcalls):
    if not name.startswith('__'):
        print(f"- {name}")

# Check types module
if hasattr(pytgcalls, 'types'):
    print("\nTypes module structure:")
    for name in dir(pytgcalls.types):
        if not name.startswith('__'):
            print(f"- {name}")

# Check if there's a stream module
if hasattr(pytgcalls, 'stream'):
    print("\nStream module structure:")
    for name in dir(pytgcalls.stream):
        if not name.startswith('__'):
            print(f"- {name}")

# Print PyTgCalls class methods
print("\nPyTgCalls class methods:")
for name, method in inspect.getmembers(pytgcalls.PyTgCalls, predicate=inspect.isfunction):
    print(f"- {name}")
