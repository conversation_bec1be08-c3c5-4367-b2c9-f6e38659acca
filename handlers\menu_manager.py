"""
Optimized Menu management for the Telegram Music Bot
Handles all menu-related functionality with performance optimizations
"""
import logging
import time
from functools import lru_cache
from typing import Optional, Dict, Any, List, Tuple
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from pyrogram.enums import ParseMode

logger = logging.getLogger("musicbot.menu_manager")

# Menu state cache for performance
_menu_cache = {}
_cache_ttl = 30  # 30 seconds cache TTL

@lru_cache(maxsize=100)
def create_button_layout(has_current_track: bool, has_queue: bool, is_playing: bool) -> List[List[InlineKeyboardButton]]:
    """Create cached button layouts for different states"""
    if has_current_track and is_playing:
        return [
            [
                InlineKeyboardButton("⏮️", callback_data="prev_song"),
                InlineKeyboardButton("⏸️", callback_data="pause_playback"),
                InlineKeyboardButton("⏭️", callback_data="skip_song")
            ],
            [
                InlineKeyboardButton("🎵 Play More", switch_inline_query_current_chat="/play "),
                InlineKeyboardButton("🎶 Queue", callback_data="show_queue")
            ],
            [
                InlineKeyboardButton("ℹ️ Help", callback_data="about")
            ]
        ]
    else:
        return [
            [
                InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play ")
            ],
            [
                InlineKeyboardButton("🎶 Queue", callback_data="show_queue"),
                InlineKeyboardButton("ℹ️ Help", callback_data="about")
            ]
        ]

def get_cached_menu_state(chat_id: int) -> Optional[Dict[str, Any]]:
    """Get cached menu state if still valid"""
    if chat_id in _menu_cache:
        cache_entry = _menu_cache[chat_id]
        if time.time() - cache_entry['timestamp'] < _cache_ttl:
            return cache_entry['data']
    return None

def cache_menu_state(chat_id: int, state: Dict[str, Any]):
    """Cache menu state for performance"""
    _menu_cache[chat_id] = {
        'data': state,
        'timestamp': time.time()
    }

async def create_main_menu(bot, message_or_chat_id, edit_message=None):
    """Create an optimized main menu with caching and smart state detection"""
    try:
        # Extract chat_id efficiently
        chat_id = message_or_chat_id if isinstance(message_or_chat_id, int) else message_or_chat_id.chat.id

        # Check cache first
        cached_state = get_cached_menu_state(chat_id)
        if cached_state and not edit_message:  # Don't use cache for edits to ensure fresh state
            menu_text = cached_state['menu_text']
            menu_buttons = cached_state['menu_buttons']
            thumbnail = cached_state.get('thumbnail')
        else:
            # Get current state efficiently
            from player.voice_chat import active_chats, is_playing

            current_track = None
            is_currently_playing = False
            has_queue = False

            if chat_id in active_chats:
                chat_data = active_chats[chat_id]
                current_track = chat_data.get('current_track')
                has_queue = bool(chat_data.get('queue'))
                is_currently_playing = await is_playing(chat_id) if current_track else False

            # Use cached button layout
            menu_buttons = create_button_layout(
                has_current_track=bool(current_track),
                has_queue=has_queue,
                is_playing=is_currently_playing
            )

            # Create optimized menu text
            if current_track and is_currently_playing:
                title = current_track.get('title', 'Unknown Track')
                menu_text = f"🎵 <b>Now Playing:</b> {title}\n\nUse the controls below:"
                thumbnail = current_track.get('thumbnail')
            else:
                menu_text = "🎵 <b>Music Bot</b>\n\nReady to play music! Use /play or the button below:"
                thumbnail = None

            # Cache the state for performance
            cache_menu_state(chat_id, {
                'menu_text': menu_text,
                'menu_buttons': menu_buttons,
                'thumbnail': thumbnail,
                'has_current_track': bool(current_track),
                'is_playing': is_currently_playing
            })

        keyboard = InlineKeyboardMarkup(menu_buttons)

        # Try to get thumbnail if track is playing
        thumbnail = None
        if current_track and is_currently_playing:
            thumbnail = current_track.get('thumbnail')

        # Send with thumbnail if available and track is playing
        if thumbnail and current_track and is_currently_playing:
            try:
                if edit_message:
                    # For edit operations, we can't change from text to photo, so just edit text
                    return await edit_message.edit_text(
                        menu_text,
                        reply_markup=keyboard,
                        parse_mode=ParseMode.HTML
                    )
                elif isinstance(message_or_chat_id, int):
                    return await bot.send_photo(
                        chat_id=message_or_chat_id,
                        photo=thumbnail,
                        caption=menu_text,
                        reply_markup=keyboard,
                        parse_mode=ParseMode.HTML
                    )
                else:
                    return await bot.send_photo(
                        chat_id=message_or_chat_id.chat.id,
                        photo=thumbnail,
                        caption=menu_text,
                        reply_markup=keyboard,
                        parse_mode=ParseMode.HTML
                    )
            except Exception as e:
                logger.warning(f"Failed to send photo, falling back to text: {e}")
                # Fallback to text message

        # Standard text message (fallback or when no thumbnail)
        if edit_message:
            return await edit_message.edit_text(
                menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
        elif isinstance(message_or_chat_id, int):
            return await bot.send_message(
                chat_id=message_or_chat_id,
                text=menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
        else:
            return await message_or_chat_id.reply(
                menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )

    except Exception as e:
        logger.error(f"Error creating main menu: {e}")
        return None

async def create_playback_controls_menu(bot, message, chat_id):
    """Redirect to main menu since controls are now integrated"""
    try:
        # Since playback controls are now integrated into the main menu,
        # just redirect to the main menu
        await create_main_menu(bot, chat_id, edit_message=message)

    except Exception as e:
        logger.error(f"Error redirecting to main menu: {e}")
        await message.edit_text("❌ Failed to load controls.")

async def create_queue_menu(bot, message, chat_id):
    """Create an optimized queue management menu"""
    try:
        # Use optimized queue manager
        try:
            from utils.queue_manager import queue_manager
            queue_status = queue_manager.get_queue_status(chat_id)

            current_track = queue_status['current_track']
            queue_items = queue_status['queue_items']
            queue_length = queue_status['queue_length']
        except ImportError:
            # Fallback to old system
            from player.voice_chat import get_queue, active_chats
            queue = await get_queue(chat_id)
            queue_items = queue
            queue_length = len(queue)

            current_track = None
            if chat_id in active_chats and active_chats[chat_id].get('current_track'):
                current_track = active_chats[chat_id]['current_track']

        # Build optimized queue text
        if current_track:
            title = current_track.get('title', 'Unknown Track')
            queue_text = f"🎵 <b>Playing:</b> {title[:30]}{'...' if len(title) > 30 else ''}\n\n"
        else:
            queue_text = "⏹️ <b>Nothing Playing</b>\n\n"

        if queue_items:
            queue_text += f"📋 <b>Up Next ({queue_length}):</b>\n"
            # Show max 5 songs for performance
            display_items = queue_items[:5]
            for idx, song in enumerate(display_items, 1):
                title = song.get('title', 'Unknown')
                title_short = title[:25] + ("..." if len(title) > 25 else "")
                queue_text += f"{idx}. {title_short}\n"

            if queue_length > 5:
                queue_text += f"\n... and {queue_length - 5} more"
        else:
            queue_text += "📋 <b>Queue is empty</b>\n\nUse /play to add songs!"

        # Create optimized button layout
        buttons = []

        # Essential controls with smart layout
        if current_track or queue_items:
            control_row = []
            if current_track:
                control_row.extend([
                    InlineKeyboardButton("⏮️", callback_data="prev_song"),
                    InlineKeyboardButton("⏸️", callback_data="pause_playback"),
                    InlineKeyboardButton("⏭️", callback_data="skip_song")
                ])
            if queue_items:
                control_row.append(InlineKeyboardButton("🗑️", callback_data="clear_queue"))

            # Optimize button layout
            if len(control_row) <= 3:
                buttons.append(control_row)
            else:
                buttons.append(control_row[:3])
                buttons.append(control_row[3:])

        # Navigation row
        buttons.append([
            InlineKeyboardButton("🎵 Add Music", switch_inline_query_current_chat="/play "),
            InlineKeyboardButton("⬅️ Back", callback_data="main_menu")
        ])

        await message.edit_text(
            queue_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error creating queue menu: {e}")
        await message.edit_text("❌ Failed to load queue.")

async def create_settings_menu(bot, message):
    """Create a minimal settings menu"""
    try:
        settings_text = (
            "⚙️ <b>Settings</b>\n\n"
            "Music bot information and help:"
        )

        buttons = [
            [
                InlineKeyboardButton("ℹ️ Help", callback_data="about"),
                InlineKeyboardButton("📋 Commands", callback_data="commands")
            ],
            [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
        ]

        await message.edit_text(
            settings_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error creating settings menu: {e}")
        await message.edit_text("❌ Failed to load settings.")

async def create_help_menu(bot, message):
    """Create a streamlined help menu"""
    try:
        help_text = (
            "ℹ️ <b>Music Bot Help</b>\n\n"
            "<b>🎵 How to use:</b>\n"
            "• Type /play followed by a song name\n"
            "• Use the ⏮️ ⏸️ ⏭️ buttons to control playback\n"
            "• View your queue with the 🎶 button\n\n"

            "<b>📋 Quick Commands:</b>\n"
            "• /play <song> - Play music\n"
            "• /skip - Skip current song\n"
            "• /queue - Show queue\n"
            "• /menu - Main menu"
        )

        buttons = [
            [
                InlineKeyboardButton("🎵 Start Playing", switch_inline_query_current_chat="/play "),
                InlineKeyboardButton("⬅️ Back", callback_data="main_menu")
            ]
        ]

        await message.edit_text(
            help_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error creating help menu: {e}")
        await message.edit_text("❌ Failed to load help.")
