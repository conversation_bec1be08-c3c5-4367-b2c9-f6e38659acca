"""
Menu management for the Telegram Music Bot
Handles all menu-related functionality with playlist options removed
"""
import logging
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from pyrogram.enums import ParseMode

logger = logging.getLogger("musicbot.menu_manager")

async def create_main_menu(bot, message_or_chat_id, edit_message=None):
    """Create a streamlined main menu that shows current track if playing"""
    try:
        # Get current playing track info
        from player.voice_chat import active_chats, is_playing

        chat_id = None
        if isinstance(message_or_chat_id, int):
            chat_id = message_or_chat_id
        else:
            chat_id = message_or_chat_id.chat.id

        current_track = None
        is_currently_playing = False

        if chat_id in active_chats:
            current_track = active_chats[chat_id].get('current_track')
            is_currently_playing = await is_playing(chat_id)

        # Create menu based on current state
        if current_track and is_currently_playing:
            # Show current track with integrated controls
            menu_text = f"🎵 <b>Now Playing:</b> {current_track['title']}\n\nUse the controls below:"

            menu_buttons = [
                # Integrated playback controls in main menu
                [
                    InlineKeyboardButton("⏮️", callback_data="prev_song"),
                    InlineKeyboardButton("⏸️", callback_data="pause_playback"),
                    InlineKeyboardButton("⏭️", callback_data="skip_song")
                ],
                # Essential functions
                [
                    InlineKeyboardButton("🎵 Play More", switch_inline_query_current_chat="/play "),
                    InlineKeyboardButton("🎶 Queue", callback_data="show_queue")
                ],
                # Quick access
                [
                    InlineKeyboardButton("ℹ️ Help", callback_data="about")
                ]
            ]
        else:
            # Standard menu when nothing is playing
            menu_text = "🎵 <b>Music Bot</b>\n\nReady to play music! Use /play or the button below:"

            menu_buttons = [
                [
                    InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play ")
                ],
                [
                    InlineKeyboardButton("🎶 Queue", callback_data="show_queue"),
                    InlineKeyboardButton("ℹ️ Help", callback_data="about")
                ]
            ]

        keyboard = InlineKeyboardMarkup(menu_buttons)

        # Try to get thumbnail if track is playing
        thumbnail = None
        if current_track and is_currently_playing:
            thumbnail = current_track.get('thumbnail')

        # Send with thumbnail if available and track is playing
        if thumbnail and current_track and is_currently_playing:
            try:
                if edit_message:
                    # For edit operations, we can't change from text to photo, so just edit text
                    return await edit_message.edit_text(
                        menu_text,
                        reply_markup=keyboard,
                        parse_mode=ParseMode.HTML
                    )
                elif isinstance(message_or_chat_id, int):
                    return await bot.send_photo(
                        chat_id=message_or_chat_id,
                        photo=thumbnail,
                        caption=menu_text,
                        reply_markup=keyboard,
                        parse_mode=ParseMode.HTML
                    )
                else:
                    return await bot.send_photo(
                        chat_id=message_or_chat_id.chat.id,
                        photo=thumbnail,
                        caption=menu_text,
                        reply_markup=keyboard,
                        parse_mode=ParseMode.HTML
                    )
            except Exception as e:
                logger.warning(f"Failed to send photo, falling back to text: {e}")
                # Fallback to text message

        # Standard text message (fallback or when no thumbnail)
        if edit_message:
            return await edit_message.edit_text(
                menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
        elif isinstance(message_or_chat_id, int):
            return await bot.send_message(
                chat_id=message_or_chat_id,
                text=menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
        else:
            return await message_or_chat_id.reply(
                menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )

    except Exception as e:
        logger.error(f"Error creating main menu: {e}")
        return None

async def create_playback_controls_menu(bot, message, chat_id):
    """Redirect to main menu since controls are now integrated"""
    try:
        # Since playback controls are now integrated into the main menu,
        # just redirect to the main menu
        await create_main_menu(bot, chat_id, edit_message=message)

    except Exception as e:
        logger.error(f"Error redirecting to main menu: {e}")
        await message.edit_text("❌ Failed to load controls.")

async def create_queue_menu(bot, message, chat_id):
    """Create the queue management menu"""
    try:
        from player.voice_chat import get_queue, active_chats

        queue = await get_queue(chat_id)

        # Get current playing track if any
        current_track = None
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            current_track = active_chats[chat_id]['current_track']

        # Build simplified queue text
        if current_track:
            queue_text = f"🎵 <b>Playing:</b> {current_track['title']}\n\n"
        else:
            queue_text = "⏹️ <b>Nothing Playing</b>\n\n"

        if queue:
            queue_text += f"📋 <b>Up Next ({len(queue)}):</b>\n"
            for idx, song in enumerate(queue[:5], 1):  # Show max 5 songs
                title_short = song['title'][:25] + ("..." if len(song['title']) > 25 else "")
                queue_text += f"{idx}. {title_short}\n"

            if len(queue) > 5:
                queue_text += f"\n... and {len(queue) - 5} more"
        else:
            queue_text += "📋 <b>Queue is empty</b>\n\nUse /play to add songs!"

        # Create streamlined buttons
        buttons = []

        # Essential controls only
        if current_track or queue:
            control_row = []
            if current_track:
                control_row.extend([
                    InlineKeyboardButton("⏮️", callback_data="prev_song"),
                    InlineKeyboardButton("⏸️", callback_data="pause_playback"),
                    InlineKeyboardButton("⏭️", callback_data="skip_song")
                ])
            if queue:
                control_row.append(InlineKeyboardButton("🗑️", callback_data="clear_queue"))

            if control_row:
                # Split into rows if too many buttons
                if len(control_row) > 3:
                    buttons.append(control_row[:3])
                    buttons.append(control_row[3:])
                else:
                    buttons.append(control_row)

        # Simple navigation
        buttons.append([
            InlineKeyboardButton("🎵 Add Music", switch_inline_query_current_chat="/play "),
            InlineKeyboardButton("⬅️ Back", callback_data="main_menu")
        ])

        await message.edit_text(
            queue_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error creating queue menu: {e}")
        await message.edit_text("❌ Failed to load queue.")

async def create_settings_menu(bot, message):
    """Create a minimal settings menu"""
    try:
        settings_text = (
            "⚙️ <b>Settings</b>\n\n"
            "Music bot information and help:"
        )

        buttons = [
            [
                InlineKeyboardButton("ℹ️ Help", callback_data="about"),
                InlineKeyboardButton("📋 Commands", callback_data="commands")
            ],
            [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
        ]

        await message.edit_text(
            settings_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error creating settings menu: {e}")
        await message.edit_text("❌ Failed to load settings.")

async def create_help_menu(bot, message):
    """Create a streamlined help menu"""
    try:
        help_text = (
            "ℹ️ <b>Music Bot Help</b>\n\n"
            "<b>🎵 How to use:</b>\n"
            "• Type /play followed by a song name\n"
            "• Use the ⏮️ ⏸️ ⏭️ buttons to control playback\n"
            "• View your queue with the 🎶 button\n\n"

            "<b>📋 Quick Commands:</b>\n"
            "• /play <song> - Play music\n"
            "• /skip - Skip current song\n"
            "• /queue - Show queue\n"
            "• /menu - Main menu"
        )

        buttons = [
            [
                InlineKeyboardButton("🎵 Start Playing", switch_inline_query_current_chat="/play "),
                InlineKeyboardButton("⬅️ Back", callback_data="main_menu")
            ]
        ]

        await message.edit_text(
            help_text,
            reply_markup=InlineKeyboardMarkup(buttons),
            parse_mode=ParseMode.HTML
        )

    except Exception as e:
        logger.error(f"Error creating help menu: {e}")
        await message.edit_text("❌ Failed to load help.")
