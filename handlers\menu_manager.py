"""
Menu management for the Telegram Music Bot
Handles all menu-related functionality with playlist options removed
"""
import logging
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from pyrogram.enums import ParseMode

logger = logging.getLogger("musicbot.menu_manager")

async def create_main_menu(bot, message_or_chat_id, edit_message=None):
    """Create the main menu without playlist options"""
    try:
        # Create a streamlined menu focused on music playback
        menu_buttons = [
            # Music playback controls
            [
                InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play "),
                InlineKeyboardButton("⏯️ Controls", callback_data="playback_controls")
            ],
            # Queue management
            [
                InlineKeyboardButton("🎶 Queue", callback_data="show_queue"),
                InlineKeyboardButton("🔄 Skip", callback_data="skip_song")
            ],
            # Advanced features
            [
                InlineKeyboardButton("🔀 Shuffle", callback_data="shuffle_queue"),
                InlineKeyboardButton("🔁 Repeat", callback_data="toggle_repeat")
            ],
            # Settings and info
            [
                InlineKeyboardButton("🔊 Volume", callback_data="volume_controls"),
                InlineKeyboardButton("⚙️ Settings", callback_data="settings")
            ],
            # Help and info
            [
                InlineKeyboardButton("ℹ️ Help", callback_data="about"),
                InlineKeyboardButton("📊 Stats", callback_data="show_stats")
            ]
        ]
        
        menu_text = (
            "🎵 <b>Music Bot Control Panel</b>\n\n"
            "Welcome to your music control center! Use the buttons below to:\n"
            "• Play music from YouTube, Spotify, or SoundCloud\n"
            "• Control playback (pause, skip, shuffle)\n"
            "• Manage your music queue\n"
            "• Adjust volume and settings\n\n"
            "Type /play followed by a song name to get started!"
        )
        
        keyboard = InlineKeyboardMarkup(menu_buttons)
        
        if edit_message:
            return await edit_message.edit_text(
                menu_text, 
                reply_markup=keyboard, 
                parse_mode=ParseMode.HTML
            )
        elif isinstance(message_or_chat_id, int):
            return await bot.send_message(
                chat_id=message_or_chat_id,
                text=menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
        else:
            return await message_or_chat_id.reply(
                menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
            
    except Exception as e:
        logger.error(f"Error creating main menu: {e}")
        return None

async def create_playback_controls_menu(bot, message, chat_id):
    """Create the playback controls menu"""
    try:
        from player.voice_chat import active_chats, is_playing
        
        is_currently_playing = await is_playing(chat_id)
        current_track = None
        
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            current_track = active_chats[chat_id]['current_track']
            
        if current_track and is_currently_playing:
            # Calculate elapsed time
            import time
            elapsed = time.time() - current_track.get('start_time', 0)
            duration = current_track.get('duration', 0)
            
            # Format time
            if duration > 0:
                elapsed_mins, elapsed_secs = divmod(int(elapsed), 60)
                total_mins, total_secs = divmod(int(duration), 60)
                time_str = f"({elapsed_mins}:{elapsed_secs:02d}/{total_mins}:{total_secs:02d})"
            else:
                time_str = ""
                
            controls_text = (
                f"🎵 <b>Now Playing:</b> {current_track['title']}\n"
                f"⏱️ {time_str}\n\n"
                f"Use the controls below to manage playback:"
            )
            
            # Create enhanced playback control buttons
            buttons = [
                [
                    InlineKeyboardButton("⏮️", callback_data="prev_song"),
                    InlineKeyboardButton("⏸️", callback_data="pause_playback"),
                    InlineKeyboardButton("⏭️", callback_data="skip_song")
                ],
                [
                    InlineKeyboardButton("🔄", callback_data="refresh_now_playing"),
                    InlineKeyboardButton("🎶", callback_data="show_queue"),
                    InlineKeyboardButton("🔀", callback_data="shuffle_queue")
                ],
                [
                    InlineKeyboardButton("🔊", callback_data="volume_controls"),
                    InlineKeyboardButton("🔁", callback_data="toggle_repeat")
                ],
                [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
            ]
        else:
            controls_text = (
                "⏹️ <b>Nothing Playing</b>\n\n"
                "There's no music playing right now. Use /play to start playing music!"
            )
            
            # Create simplified buttons when nothing is playing
            buttons = [
                [
                    InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play "),
                    InlineKeyboardButton("🎶 Queue", callback_data="show_queue")
                ],
                [
                    InlineKeyboardButton("🔀 Shuffle", callback_data="shuffle_queue"),
                    InlineKeyboardButton("🔁 Repeat", callback_data="toggle_repeat")
                ],
                [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
            ]
            
        await message.edit_text(
            controls_text, 
            reply_markup=InlineKeyboardMarkup(buttons), 
            parse_mode=ParseMode.HTML
        )
        
    except Exception as e:
        logger.error(f"Error creating playback controls menu: {e}")
        await message.edit_text("❌ Failed to load playback controls.")

async def create_queue_menu(bot, message, chat_id):
    """Create the queue management menu"""
    try:
        from player.voice_chat import get_queue, active_chats
        
        queue = await get_queue(chat_id)
        
        # Get current playing track if any
        current_track = None
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            current_track = active_chats[chat_id]['current_track']
        
        # Build queue text
        if current_track:
            queue_text = f"🎵 <b>Currently Playing:</b> {current_track['title']}\n\n"
        else:
            queue_text = "⏹️ <b>Nothing Currently Playing</b>\n\n"
        
        if queue:
            queue_text += f"📋 <b>Queue ({len(queue)} songs):</b>\n"
            for idx, song in enumerate(queue[:10], 1):  # Show max 10 songs
                title_short = song['title'][:30] + ("..." if len(song['title']) > 30 else "")
                queue_text += f"{idx}. {title_short}\n"
            
            if len(queue) > 10:
                queue_text += f"\n... and {len(queue) - 10} more songs"
        else:
            queue_text += "📋 <b>Queue is empty</b>\n\nAdd songs using /play command!"
        
        # Create buttons for queue management
        buttons = []
        
        # Add buttons for each song in queue (first 5)
        for idx, song in enumerate(queue[:5], 1):
            title_short = song['title'][:20] + ("..." if len(song['title']) > 20 else "")
            buttons.append([
                InlineKeyboardButton(f"{idx}. {title_short}", callback_data=f"queue_info_{idx}"),
                InlineKeyboardButton("❌", callback_data=f"queue_remove_{idx}")
            ])
        
        # Add queue control buttons
        control_buttons = []
        if current_track:
            control_buttons = [
                InlineKeyboardButton("⏸️", callback_data="pause_playback"),
                InlineKeyboardButton("⏭️", callback_data="skip_song")
            ]
        
        if queue:
            control_buttons.extend([
                InlineKeyboardButton("🔀", callback_data="shuffle_queue"),
                InlineKeyboardButton("🗑️", callback_data="clear_queue")
            ])
        
        if control_buttons:
            # Split into rows of 3 buttons max
            for i in range(0, len(control_buttons), 3):
                buttons.append(control_buttons[i:i+3])
        
        # Add navigation buttons
        buttons.append([
            InlineKeyboardButton("🔄 Refresh", callback_data="show_queue"),
            InlineKeyboardButton("⬅️ Back", callback_data="main_menu")
        ])
        
        await message.edit_text(
            queue_text, 
            reply_markup=InlineKeyboardMarkup(buttons), 
            parse_mode=ParseMode.HTML
        )
        
    except Exception as e:
        logger.error(f"Error creating queue menu: {e}")
        await message.edit_text("❌ Failed to load queue.")

async def create_settings_menu(bot, message):
    """Create the settings menu"""
    try:
        settings_text = (
            "⚙️ <b>Settings</b>\n\n"
            "Configure your music bot preferences:"
        )
        
        buttons = [
            [
                InlineKeyboardButton("🔊 Volume", callback_data="volume_controls"),
                InlineKeyboardButton("🎚️ Audio Quality", callback_data="audio_quality")
            ],
            [
                InlineKeyboardButton("🔁 Repeat Mode", callback_data="toggle_repeat"),
                InlineKeyboardButton("🔀 Auto Shuffle", callback_data="toggle_auto_shuffle")
            ],
            [
                InlineKeyboardButton("📱 Notifications", callback_data="notification_settings"),
                InlineKeyboardButton("🎵 Default Source", callback_data="default_source")
            ],
            [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
        ]
        
        await message.edit_text(
            settings_text, 
            reply_markup=InlineKeyboardMarkup(buttons), 
            parse_mode=ParseMode.HTML
        )
        
    except Exception as e:
        logger.error(f"Error creating settings menu: {e}")
        await message.edit_text("❌ Failed to load settings.")

async def create_help_menu(bot, message):
    """Create the help menu"""
    try:
        help_text = (
            "ℹ️ <b>Music Bot Help</b>\n\n"
            "<b>🎵 Playing Music:</b>\n"
            "• /play <song name> - Play a song\n"
            "• /play <YouTube URL> - Play from URL\n"
            "• Use inline mode: @botname <song>\n\n"
            
            "<b>⏯️ Playback Controls:</b>\n"
            "• Use the inline buttons for pause/resume\n"
            "• Skip songs with ⏭️ button\n"
            "• View queue with 🎶 button\n\n"
            
            "<b>🔀 Advanced Features:</b>\n"
            "• Shuffle queue with 🔀\n"
            "• Repeat modes with 🔁\n"
            "• Volume control with 🔊\n\n"
            
            "<b>📋 Commands:</b>\n"
            "• /menu - Show main menu\n"
            "• /queue - Show current queue\n"
            "• /skip - Skip current song\n"
            "• /stop - Stop playback\n"
            "• /help - Show this help"
        )
        
        buttons = [
            [
                InlineKeyboardButton("🎵 Try Playing", switch_inline_query_current_chat="/play "),
                InlineKeyboardButton("📋 Main Menu", callback_data="main_menu")
            ]
        ]
        
        await message.edit_text(
            help_text, 
            reply_markup=InlineKeyboardMarkup(buttons), 
            parse_mode=ParseMode.HTML
        )
        
    except Exception as e:
        logger.error(f"Error creating help menu: {e}")
        await message.edit_text("❌ Failed to load help.")
