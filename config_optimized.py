"""
Optimized Configuration Module for Telegram Music Bot
Provides performance-optimized settings and caching
"""

import os
from functools import lru_cache
from typing import Dict, Any, Optional

# Performance Settings
PERFORMANCE_CONFIG = {
    # Cache settings
    'file_cache_size': 1000,
    'metadata_cache_ttl': 3600,  # 1 hour
    'search_cache_ttl': 1800,    # 30 minutes
    
    # Download settings
    'max_concurrent_downloads': 3,
    'download_timeout': 30,
    'min_playable_percentage': 5,
    
    # Voice chat settings
    'join_timeout': 10,
    'playback_timeout': 5,
    'queue_worker_interval': 0.5,
    
    # Memory optimization
    'max_queue_size': 50,
    'max_history_size': 20,
    'cleanup_interval': 3600,  # 1 hour
    
    # Logging optimization
    'log_level': 'WARNING',
    'max_log_files': 5,
    'log_rotation_size': '10MB',
}

# Audio Quality Presets (optimized for performance)
AUDIO_QUALITY_PRESETS = {
    'low': {
        'format': 'bestaudio[ext=m4a]/bestaudio[ext=mp3]/bestaudio',
        'quality': '128',
        'codec': 'mp3'
    },
    'medium': {
        'format': 'bestaudio[ext=m4a]/bestaudio[ext=mp3]/bestaudio',
        'quality': '192',
        'codec': 'mp3'
    },
    'high': {
        'format': 'bestaudio[ext=m4a]/bestaudio[ext=mp3]/bestaudio',
        'quality': '320',
        'codec': 'mp3'
    }
}

# Optimized yt-dlp options
@lru_cache(maxsize=1)
def get_optimized_ydl_opts(quality: str = 'medium') -> Dict[str, Any]:
    """Get optimized yt-dlp options with caching"""
    preset = AUDIO_QUALITY_PRESETS.get(quality, AUDIO_QUALITY_PRESETS['medium'])
    
    return {
        'format': preset['format'],
        'outtmpl': '%(title)s.%(ext)s',
        'extractaudio': True,
        'audioformat': preset['codec'],
        'audioquality': preset['quality'],
        'embed_subs': False,
        'writesubtitles': False,
        'writeautomaticsub': False,
        'writedescription': False,
        'writeinfojson': False,
        'writethumbnail': False,
        'no_warnings': True,
        'quiet': True,
        'no_color': True,
        'extract_flat': False,
        'ignoreerrors': True,
        'socket_timeout': 30,
        'retries': 3,
        'fragment_retries': 3,
        'skip_unavailable_fragments': True,
        'keep_fragments': False,
        'buffersize': 1024 * 16,  # 16KB buffer
        'http_chunk_size': 1024 * 1024,  # 1MB chunks
    }

# File path optimization
@lru_cache(maxsize=500)
def get_safe_filename(title: str) -> str:
    """Generate safe filename with caching"""
    import re
    return re.sub(r'[^\w\-_\. ]', '_', title)

@lru_cache(maxsize=1000)
def resolve_audio_file_path(base_path: str) -> Optional[str]:
    """Resolve actual audio file path with caching"""
    possible_files = [
        base_path,
        base_path + ".mp3",
        base_path.replace('.mp3', '.mp3.mp3'),
    ]
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            return os.path.abspath(file_path)
    return None

# Directory optimization
@lru_cache(maxsize=1)
def get_optimized_directories() -> Dict[str, str]:
    """Get optimized directory structure"""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    return {
        'base': base_dir,
        'downloads': os.path.join(base_dir, 'downloaded_songs'),
        'temp': os.path.join(base_dir, 'temp'),
        'cache': os.path.join(base_dir, 'cache'),
        'logs': os.path.join(base_dir, 'logs'),
        'ffmpeg': os.path.join(base_dir, 'ffmpeg', 'bin'),
    }

# Memory optimization utilities
class MemoryOptimizer:
    """Memory optimization utilities"""
    
    @staticmethod
    def clear_caches():
        """Clear all LRU caches"""
        get_optimized_ydl_opts.cache_clear()
        get_safe_filename.cache_clear()
        resolve_audio_file_path.cache_clear()
        get_optimized_directories.cache_clear()
    
    @staticmethod
    def get_cache_info():
        """Get cache statistics"""
        return {
            'ydl_opts': get_optimized_ydl_opts.cache_info(),
            'safe_filename': get_safe_filename.cache_info(),
            'audio_file_path': resolve_audio_file_path.cache_info(),
            'directories': get_optimized_directories.cache_info(),
        }

# Performance monitoring
class PerformanceMonitor:
    """Simple performance monitoring"""
    
    def __init__(self):
        self.metrics = {
            'downloads_started': 0,
            'downloads_completed': 0,
            'downloads_failed': 0,
            'voice_chat_joins': 0,
            'songs_played': 0,
            'cache_hits': 0,
            'cache_misses': 0,
        }
    
    def increment(self, metric: str):
        """Increment a metric"""
        if metric in self.metrics:
            self.metrics[metric] += 1
    
    def get_metrics(self) -> Dict[str, int]:
        """Get current metrics"""
        return self.metrics.copy()
    
    def reset_metrics(self):
        """Reset all metrics"""
        for key in self.metrics:
            self.metrics[key] = 0

# Global instances
memory_optimizer = MemoryOptimizer()
performance_monitor = PerformanceMonitor()

# Optimized logging setup
def setup_optimized_logging():
    """Setup optimized logging configuration"""
    import logging
    from logging.handlers import RotatingFileHandler
    
    # Create logs directory
    dirs = get_optimized_directories()
    os.makedirs(dirs['logs'], exist_ok=True)
    
    # Setup rotating file handler
    log_file = os.path.join(dirs['logs'], 'musicbot.log')
    handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=PERFORMANCE_CONFIG['max_log_files']
    )
    
    # Set format
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    handler.setFormatter(formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, PERFORMANCE_CONFIG['log_level']))
    root_logger.addHandler(handler)
    
    return root_logger

# Cleanup utilities
async def cleanup_temp_files():
    """Cleanup temporary files"""
    import time
    import glob
    
    dirs = get_optimized_directories()
    temp_dir = dirs['temp']
    
    if not os.path.exists(temp_dir):
        return 0
    
    cleaned = 0
    current_time = time.time()
    
    # Remove files older than 1 hour
    for file_path in glob.glob(os.path.join(temp_dir, '*')):
        try:
            if os.path.isfile(file_path):
                file_age = current_time - os.path.getmtime(file_path)
                if file_age > 3600:  # 1 hour
                    os.remove(file_path)
                    cleaned += 1
        except Exception:
            pass  # Ignore errors
    
    return cleaned

# Export optimized configuration
__all__ = [
    'PERFORMANCE_CONFIG',
    'AUDIO_QUALITY_PRESETS',
    'get_optimized_ydl_opts',
    'get_safe_filename',
    'resolve_audio_file_path',
    'get_optimized_directories',
    'memory_optimizer',
    'performance_monitor',
    'setup_optimized_logging',
    'cleanup_temp_files',
]
