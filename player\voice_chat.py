"""
Voice chat functionality for the Telegram Music Bot

This module provides the necessary functions to join and play music in Telegram voice chats
using PyTgCalls.
"""

import os
import sys
import asyncio
import subprocess
import logging

# --- Ensure ffmpeg/ffprobe are in PATH for all subprocesses ---
ffmpeg_bin_dir = os.path.abspath(os.path.join("ffmpeg", "bin"))
os.environ["PATH"] = ffmpeg_bin_dir + os.pathsep + os.environ.get("PATH", "")
os.environ["FFMPEG_BINARY"] = os.path.join(ffmpeg_bin_dir, "ffmpeg.exe")
os.environ["FFPROBE_BINARY"] = os.path.join(ffmpeg_bin_dir, "ffprobe.exe")
# ------------------------------------------------------------
from pytgcalls import PyTgCalls
from pytgcalls.types import Update
from pyrogram import Client
from pyrogram.errors import ChannelInvalid, PeerIdInvalid, UserNotParticipant
import time

# FFmpeg paths
FFMPEG_DIR = "ffmpeg"
FFMPEG_PATH = os.path.join(FFMPEG_DIR, "bin", "ffmpeg.exe")
FFPROBE_PATH = os.path.join(FFMPEG_DIR, "bin", "ffprobe.exe")

# Check if FFmpeg is installed
def check_ffmpeg():
    """Check if FFmpeg is installed and in PATH"""
    # First check if we have the local FFmpeg
    if os.path.exists(FFMPEG_PATH):
        print(f"Found local FFmpeg at: {FFMPEG_PATH}")
        try:
            result = subprocess.run(
                [FFMPEG_PATH, "-version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
            version = result.stdout.decode().split('\n')[0]
            print(f"Local FFmpeg version: {version}")
            return True
        except (subprocess.SubprocessError, FileNotFoundError) as e:
            print(f"Error running local FFmpeg: {e}")
            pass
    else:
        print(f"Local FFmpeg not found at: {FFMPEG_PATH}")

    # Fall back to system FFmpeg
    try:
        result = subprocess.run(
            ["ffmpeg", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        version = result.stdout.decode().split('\n')[0]
        print(f"System FFmpeg version: {version}")
        return True
    except (subprocess.SubprocessError, FileNotFoundError) as e:
        print(f"Error finding system FFmpeg: {e}")
        return False

# Check if FFprobe is installed
def check_ffprobe():
    """Check if FFprobe is installed and in PATH"""
    # First check if we have the local FFprobe
    if os.path.exists(FFPROBE_PATH):
        print(f"Found local FFprobe at: {FFPROBE_PATH}")
        try:
            result = subprocess.run(
                [FFPROBE_PATH, "-version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
            version = result.stdout.decode().split('\n')[0]
            print(f"Local FFprobe version: {version}")
            return True
        except (subprocess.SubprocessError, FileNotFoundError) as e:
            print(f"Error running local FFprobe: {e}")
            pass
    else:
        print(f"Local FFprobe not found at: {FFPROBE_PATH}")

    # Fall back to system FFprobe
    try:
        result = subprocess.run(
            ["ffprobe", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        version = result.stdout.decode().split('\n')[0]
        print(f"System FFprobe version: {version}")
        return True
    except (subprocess.SubprocessError, FileNotFoundError) as e:
        print(f"Error finding system FFprobe: {e}")
        return False

# Print FFmpeg status
ffmpeg_installed = check_ffmpeg()
ffprobe_installed = check_ffprobe()

if not ffmpeg_installed:
    print("⚠️ FFmpeg is not installed or not in PATH. Voice chat functionality may not work properly.")
    print("Please install FFmpeg from https://ffmpeg.org/download.html")
    print("Alternatively, run download_ffmpeg.py to download FFmpeg automatically.")

if not ffprobe_installed:
    print("⚠️ FFprobe is not installed or not in PATH. Voice chat functionality may not work properly.")
    print("FFprobe is usually included with FFmpeg installation.")
    print("Alternatively, run download_ffmpeg.py to download FFmpeg automatically.")

# Store active voice chats and their current tracks
active_chats = {}

# Store chat_id -> queue worker asyncio.Task
queue_workers = {}

# Time to wait before checking if we're in a call
JOIN_WAIT_TIME = 3.0

# Initialize PyTgCalls instance
py_tgcalls = None

# Import queue_worker from the new module
from .queue_worker import queue_worker

async def is_playing(chat_id):
    """
    Check if something is currently playing in the voice chat (PyTgCalls).
    Enhanced with better error handling and multiple verification methods.
    """
    global py_tgcalls
    logger = logging.getLogger("musicbot.voice_chat")

    # First check if chat is in active_chats
    if chat_id not in active_chats:
        logger.debug(f"is_playing: chat_id {chat_id} not in active_chats")
        return False

    # SIMPLIFIED APPROACH: Just check if current_track exists
    # This is more reliable than trying to check PyTgCalls status
    # which can be inconsistent
    has_current_track = active_chats[chat_id].get('current_track') is not None

    # Check the caller's intent - if this is called from play_in_voice_chat, we need to be strict
    import inspect
    caller_frame = inspect.currentframe().f_back
    is_queue_check = False

    if caller_frame:
        caller_name = caller_frame.f_code.co_name
        if 'play_in_voice_chat' in caller_name:
            # This is being called to check if we should queue a song
            is_queue_check = True
            logger.debug(f"is_playing called from play_in_voice_chat for queue check")
        elif 'queue_worker' in caller_name:
            # If called from queue_worker, just use the current_track check
            logger.debug(f"is_playing called from queue_worker, current_track exists: {has_current_track}")
            return has_current_track

    # For queue checks, we need to be strict about whether something is actually playing
    if is_queue_check:
        if not has_current_track:
            logger.debug(f"is_playing (queue check): No current track in chat_id {chat_id}")
            return False

        # If we have a current track, it's playing
        logger.debug(f"is_playing (queue check): Found current track in chat_id {chat_id}")
        return True

    # For other callers (like join verification), we can be more lenient
    # Check if current_track is set
    if not has_current_track:
        # For join verification, we'll return True if we're in the voice chat
        if caller_frame and 'join_voice_chat' in caller_frame.f_code.co_name:
            logger.debug(f"is_playing called from join_voice_chat, returning True for chat_id={chat_id}")
            return True
        return False

    # Check if PyTgCalls is initialized
    if py_tgcalls is None:
        logger.warning("PyTgCalls not initialized, using current_track as fallback")
        return active_chats[chat_id]['current_track'] is not None

    # Try to check PyTgCalls active calls if available
    try:
        # Method 1: Check active_calls attribute
        if hasattr(py_tgcalls, 'active_calls'):
            for call in py_tgcalls.active_calls:
                if hasattr(call, 'chat_id') and call.chat_id == chat_id:
                    # Check if the call is actually playing audio
                    if hasattr(call, 'status'):
                        if call.status == 'playing':
                            return True
                        else:
                            logger.debug(f"Call found for chat_id={chat_id} but status is {call.status}")
                            return False
                    # If we can't check status, assume it's playing if in active_calls
                    return True

        # Method 2: Try to use get_active_call method if available
        if hasattr(py_tgcalls, 'get_active_call'):
            active_call = await py_tgcalls.get_active_call(chat_id)
            if active_call:
                return True

        # Method 3: Check if there's an active group call via Telegram API
        # This is a more reliable but slower method
        try:
            # This requires the user client to be available
            if hasattr(py_tgcalls, '_app'):
                user_client = py_tgcalls._app
                if user_client:
                    try:
                        # Try to get the group call
                        call = await user_client.get_group_call(chat_id)
                        if call:
                            return True
                    except Exception as e:
                        logger.debug(f"Error checking group call via Telegram API: {e}")
        except Exception as e:
            logger.debug(f"Error accessing user client: {e}")

        # If we get here, no active call was found
        return False
    except Exception as e:
        logger.warning(f"Error checking if playing: {e}")
        # Fall back to current_track check
        pass

    # If we can't check PyTgCalls or there was an error, use current_track as fallback
    # Also check if the track hasn't been playing for too long (more than 2x duration)
    current_track = active_chats[chat_id].get('current_track')
    if current_track:
        start_time = current_track.get('start_time', 0)
        duration = current_track.get('duration', 180)
        current_time = time.time()

        # If the track has been "playing" for more than twice its duration, it's likely stuck
        if current_time - start_time > duration * 2:
            logger.warning(f"Track {current_track['title']} has been playing for too long, considering it stopped")
            return False

        return True
    return False

async def initialize_tgcalls(user_client):
    """Initialize PyTgCalls with the user client"""
    global py_tgcalls

    # Create PyTgCalls instance
    py_tgcalls = PyTgCalls(user_client)

    # Set FFmpeg and FFprobe paths as environment variables
    if os.path.exists(FFMPEG_PATH):
        print(f"Setting FFmpeg path to: {FFMPEG_PATH}")
        os.environ["FFMPEG_BINARY"] = FFMPEG_PATH

    if os.path.exists(FFPROBE_PATH):
        print(f"Setting FFprobe path to: {FFPROBE_PATH}")
        os.environ["FFPROBE_BINARY"] = FFPROBE_PATH

    # TODO: Implement queue auto-play for next song when current song finishes.
    # PyTgCalls does not support on_stream_end event directly in this version.
    # See https://github.com/pytgcalls/pytgcalls for latest event support and update code if needed.

    # Start PyTgCalls - making sure to await it properly
    await py_tgcalls.start()

    return py_tgcalls

async def get_queue(chat_id):
    """Get the current queue for a chat"""
    if chat_id in active_chats:
        return active_chats[chat_id]['queue']
    return []

async def previous_song(client, chat_id):
    """Play the previous song (if available in history)"""
    global active_chats, py_tgcalls

    if chat_id not in active_chats:
        return False, "❌ Not currently in a voice chat"

    # For now, return a message that this feature is not implemented
    # In a full implementation, you would maintain a history of played songs
    return False, "⏮️ Previous song feature coming soon!"

async def skip_current_song(client, chat_id):
    """Skip the current song and play the next one in queue"""
    global active_chats, py_tgcalls

    if chat_id not in active_chats:
        return False, "❌ Not currently in a voice chat"

    # Check if something is playing
    if not active_chats[chat_id].get('current_track'):
        return False, "❌ Nothing is currently playing"

    # Check if there are songs in the queue
    if not active_chats[chat_id]['queue']:
        # Just stop the current playback
        try:
            # Reset current track
            active_chats[chat_id]['current_track'] = None

            # Use silence file to effectively stop playback
            silence_file = "silence.mp3"
            if not os.path.exists(silence_file):
                silence_file = "https://github.com/pytgcalls/example-files/raw/master/silence.mp3"

            await py_tgcalls.play(chat_id, silence_file)
            return True, "⏭️ Skipped the current song. Queue is now empty."
        except Exception as e:
            return False, f"❌ Error skipping song: {e}"

    # Get the next song from queue
    next_song = active_chats[chat_id]['queue'].pop(0)

    # Update current track
    active_chats[chat_id]['current_track'] = {
        'title': next_song['title'],
        'url': next_song['url'],
        'start_time': time.time(),
        'notify_callback': next_song.get('notify_callback'),
        'progress_callback': next_song.get('progress_callback')
    }

    # Play the next song
    try:
        # Resolve the actual file path with better error handling
        def find_actual_file(base_path):
            """Find the actual audio file, handling various extensions"""
            if base_path.startswith(('http://', 'https://')):
                return base_path  # URL, return as-is

            possible_files = [
                base_path,  # Original path
                base_path + ".mp3",  # With .mp3 extension
                base_path.replace('.mp3', '.mp3.mp3'),  # Double extension
            ]

            for file_path in possible_files:
                if os.path.exists(file_path):
                    logger.info(f"Found actual audio file for skip: {file_path}")
                    return os.path.abspath(file_path)
            return None

        song_url = next_song['url']
        actual_file = find_actual_file(song_url)
        if actual_file:
            song_url = actual_file
            logger.info(f"Resolved skip file path to: {song_url}")
        elif not song_url.startswith(('http://', 'https://')):
            # File not found
            logger.error(f"Skip failed - file not found: {song_url}")
            return False, f"❌ Audio file not found: {os.path.basename(song_url)}"

        await py_tgcalls.play(chat_id, song_url)

        # Notify about the new song - only if we don't have a notification flag
        if next_song.get('notify_callback') and not next_song.get('_notification_sent'):
            await next_song['notify_callback'](f"🎵 Now playing: {next_song['title']}")
            # Mark as sent to prevent duplicate notifications
            next_song['_notification_sent'] = True

        return True, f"⏭️ Skipped to next song: {next_song['title']}"
    except Exception as e:
        return False, f"❌ Error playing next song: {e}"

async def show_queue(chat_id):
    """Format the current queue for display"""
    if chat_id not in active_chats or not active_chats[chat_id]['queue']:
        return "🔄 Queue is empty"

    queue_str = "🎵 **Current Queue:**\n"
    for i, song in enumerate(active_chats[chat_id]['queue'], 1):
        queue_str += f"{i}. {song['title']}\n"

    return queue_str

async def clear_queue(chat_id):
    """Clear the queue for a chat"""
    if chat_id in active_chats:
        active_chats[chat_id]['queue'] = []
        return True, "✅ Queue cleared"
    return False, "❌ Not in a voice chat"

async def remove_from_queue(chat_id, position):
    """Remove a song from the queue by position (1-based index)"""
    if chat_id not in active_chats:
        return False, "❌ Not currently in a voice chat"

    queue = active_chats[chat_id]['queue']

    # Convert to 0-based index
    index = position - 1

    if not queue or index < 0 or index >= len(queue):
        return False, "❌ Invalid queue position"

    # Get the song title before removing
    song_title = queue[index]['title']

    # Remove the song
    queue.pop(index)

    return True, f"✅ Removed '{song_title}' from queue"

# This is a duplicate function that has been replaced by the improved version above

async def join_voice_chat(client, chat_id):
    """
    Join a voice chat in a specific chat.
    Enhanced with better error handling and recovery mechanisms.
    """
    global py_tgcalls
    logger = logging.getLogger("musicbot.voice_chat")
    logger.info(f"Attempting to join voice chat in {chat_id}")

    # Check if FFmpeg and FFprobe are installed
    if not ffmpeg_installed or not ffprobe_installed:
        logger.error("FFmpeg and/or FFprobe are not installed")
        return False, "❌ FFmpeg and/or FFprobe are not installed. Run download_ffmpeg.py to download them automatically."

    # Initialize PyTgCalls if needed
    if py_tgcalls is None:
        try:
            from main import user
            logger.info("Initializing PyTgCalls")
            py_tgcalls = await initialize_tgcalls(user)
            if py_tgcalls is None:
                logger.error("Failed to initialize PyTgCalls")
                return False, "❌ Failed to initialize voice chat system"
        except Exception as e:
            logger.error(f"Error initializing PyTgCalls: {e}")
            return False, f"❌ Error initializing voice chat system: {str(e)}"

    try:
        # Check if already in the voice chat
        if chat_id in active_chats:
            # Try multiple methods to check if we're in a call with this chat_id
            is_in_call = False

            # Method 1: Check active_calls attribute
            try:
                if hasattr(py_tgcalls, 'active_calls') and py_tgcalls.active_calls:
                    for call in py_tgcalls.active_calls:
                        if hasattr(call, 'chat_id') and call.chat_id == chat_id:
                            logger.info(f"Found active call for chat {chat_id} in active_calls")
                            is_in_call = True
                            break
            except Exception as e:
                logger.warning(f"Error checking active_calls: {e}")

            # Method 2: Try to use get_active_call method if available
            if not is_in_call and hasattr(py_tgcalls, 'get_active_call'):
                try:
                    active_call = await py_tgcalls.get_active_call(chat_id)
                    if active_call:
                        logger.info(f"Found active call for chat {chat_id} using get_active_call")
                        is_in_call = True
                except Exception as e:
                    logger.warning(f"Error using get_active_call: {e}")

            # Method 3: Use is_playing function
            if not is_in_call:
                try:
                    is_in_call = await is_playing(chat_id)
                    if is_in_call:
                        logger.info(f"Detected active playback in chat {chat_id}")
                except Exception as e:
                    logger.warning(f"Error using is_playing: {e}")

            if is_in_call:
                return True, "✅ Already in the voice chat"

            # If we reach here, we're not in the call
            logger.info(f"No active call found for chat {chat_id}, removing from active_chats")
            if chat_id in active_chats:
                del active_chats[chat_id]

        # Initialize active chat entry
        active_chats[chat_id] = {
            'current_track': None,
            'queue': []
        }

        # Use a local silence file if available, otherwise use the remote one
        silence_file = "silence.mp3"
        if not os.path.exists(silence_file):
            silence_file = "https://github.com/pytgcalls/example-files/raw/master/silence.mp3"
            logger.info(f"Using remote silence file: {silence_file}")
        else:
            logger.info(f"Using local silence file: {silence_file}")

        # For PyTgCalls, we use the play method to join a voice chat
        max_retries = 3
        for retry in range(max_retries):
            try:
                logger.info(f"Joining voice chat in {chat_id} (attempt {retry+1}/{max_retries})")
                await py_tgcalls.play(chat_id, silence_file)

                # Wait a moment to ensure we've joined
                await asyncio.sleep(JOIN_WAIT_TIME)

                # The verification is failing even though we're actually joining
                # Let's assume success after a successful play() call and just return
                logger.info(f"Successfully joined voice chat in {chat_id} (attempt {retry+1})")

                # Add to active_chats to ensure we're tracked properly
                if chat_id not in active_chats:
                    active_chats[chat_id] = {
                        'current_track': None,
                        'queue': []
                    }

                return True, "✅ Successfully joined the voice chat. Use /playvc to play music."

                # The following verification code is commented out as it's causing false negatives
                """
                is_joined = False
                try:
                    if hasattr(py_tgcalls, 'active_calls'):
                        for call in py_tgcalls.active_calls:
                            if hasattr(call, 'chat_id') and call.chat_id == chat_id:
                                is_joined = True
                                break
                except Exception:
                    pass

                if is_joined:
                    logger.info(f"Successfully joined voice chat in {chat_id}")
                    return True, "✅ Successfully joined the voice chat. Use /playvc to play music."
                else:
                    logger.warning(f"Join attempt {retry+1} succeeded but verification failed, retrying...")
                    await asyncio.sleep(1.0)
                """
            except Exception as e:
                error_message = str(e)
                logger.error(f"Error joining voice chat (attempt {retry+1}): {error_message}")

                if retry < max_retries - 1:
                    logger.info(f"Retrying in 2 seconds...")
                    await asyncio.sleep(2.0)
                else:
                    if chat_id in active_chats:
                        del active_chats[chat_id]
                    return False, f"❌ Failed to join voice chat after {max_retries} attempts: {error_message}"

        # If we get here, all retries failed but didn't raise exceptions
        if chat_id in active_chats:
            del active_chats[chat_id]
        return False, f"❌ Failed to join voice chat after {max_retries} attempts"

    except ChannelInvalid:
        logger.error(f"Channel invalid error for chat {chat_id}")
        return False, "❌ The voice chat is not active in this chat. Start a voice chat first."
    except UserNotParticipant:
        logger.error(f"User not participant error for chat {chat_id}")
        return False, "❌ The user account is not in the chat. Add the user to this chat."
    except PeerIdInvalid:
        logger.error(f"Peer ID invalid error for chat {chat_id}")
        return False, "❌ Invalid chat ID or the bot cannot access this chat."
    except Exception as e:
        error_message = str(e)
        logger.error(f"Unexpected error joining voice chat: {error_message}")
        if "ffprobe not installed" in error_message:
            return False, "❌ FFprobe is not installed. Run download_ffmpeg.py to download it automatically."
        elif "ffmpeg not installed" in error_message:
            return False, "❌ FFmpeg is not installed. Run download_ffmpeg.py to download it automatically."
        else:
            return False, f"❌ Error joining voice chat: {error_message}"

async def play_in_voice_chat(client, chat_id, audio_url, title, notify_callback=None, progress_callback=None):
    """
    Play audio in a voice chat or add to queue if something is already playing.
    Enhanced with better error handling, progress reporting, and queue management.
    """
    global py_tgcalls, queue_workers
    logger = logging.getLogger("musicbot.voice_chat")

    # Check if FFmpeg and FFprobe are installed
    if not ffmpeg_installed or not ffprobe_installed:
        return False, "❌ FFmpeg and/or FFprobe are not installed. Run download_ffmpeg.py to download them automatically."

    if py_tgcalls is None:
        try:
            from main import user
            py_tgcalls = await initialize_tgcalls(user)
        except Exception as e:
            logger.error(f"Failed to initialize PyTgCalls: {e}")
            return False, f"❌ Failed to initialize voice chat system: {e}"

    try:
        # Check if we're already in the voice chat
        if chat_id not in active_chats:
            # Join the voice chat first
            logger.info(f"Not in voice chat {chat_id}, attempting to join...")
            join_success, join_message = await join_voice_chat(client, chat_id)
            if not join_success:
                logger.error(f"Failed to join voice chat {chat_id}: {join_message}")
                return False, f"❌ Failed to join voice chat: {join_message}"
            logger.info(f"Successfully joined voice chat {chat_id}")

        # Check if this is a file that's currently being downloaded
        from main import active_downloads, MIN_PLAYABLE_PERCENTAGE
        is_downloading = False
        partial_file = None
        download_progress = 0

        if title in active_downloads:
            download_info = active_downloads[title]
            download_progress = download_info.get('progress', 0)
            final_path = download_info.get('final_path')

            # Check if we have a partial file that's ready for playback
            if download_progress >= MIN_PLAYABLE_PERCENTAGE and final_path:
                partial_file = f"{final_path}.partial"
                if os.path.exists(partial_file):
                    logger.info(f"Using partially downloaded file for '{title}' ({download_progress:.1f}% complete)")
                    is_downloading = True
                    # Use the partial file for playback
                    audio_url = partial_file

        # Prepare song info
        song_info = {
            'title': title,
            'url': audio_url,
            'notify_callback': notify_callback,
            'progress_callback': progress_callback,
            'added_at': time.time(),
            'is_downloading': is_downloading,
            'partial_file': partial_file,
            'download_progress': download_progress
        }

        # Check if something is already playing
        is_currently_playing = await is_playing(chat_id)
        if is_currently_playing:
            # Add to queue
            active_chats[chat_id]['queue'].append(song_info)
            queue_position = len(active_chats[chat_id]['queue'])
            logger.info(f"Added '{title}' to queue at position {queue_position} in chat {chat_id}")

            # Start queue worker if not running
            from asyncio import create_task
            if chat_id not in queue_workers or queue_workers[chat_id].done():
                logger.info(f"Starting queue worker for chat {chat_id}")
                queue_workers[chat_id] = create_task(queue_worker(chat_id))

            status_msg = f"🎵 Added to queue at position {queue_position}: {title}"
            return True, status_msg

        # If nothing is playing, play immediately
        logger.info(f"Nothing currently playing in chat {chat_id}, playing '{title}' immediately")

        # Double-check that we're not already playing something
        # This is a safety check to ensure we don't start playing if something is already playing
        if active_chats[chat_id].get('current_track') is not None:
            logger.warning(f"Found existing current_track in chat {chat_id} despite is_playing returning False")
            # Add to queue instead
            active_chats[chat_id]['queue'].append(song_info)
            queue_position = len(active_chats[chat_id]['queue'])
            logger.info(f"Added '{title}' to queue at position {queue_position} in chat {chat_id}")

            # Start queue worker if not running
            from asyncio import create_task
            if chat_id not in queue_workers or queue_workers[chat_id].done():
                logger.info(f"Starting queue worker for chat {chat_id}")
                queue_workers[chat_id] = create_task(queue_worker(chat_id))

            status_msg = f"🎵 Added to queue at position {queue_position}: {title}"
            return True, status_msg

        # Get duration if it's a local file
        duration = None
        try:
            import os, mutagen
            if os.path.isfile(audio_url):
                audio = mutagen.File(audio_url)
                if audio:
                    duration = int(audio.info.length)
        except Exception as e:
            logger.warning(f"Could not get duration for {audio_url}: {e}")

        # Store track info with more details - do this BEFORE playing to ensure proper queue behavior
        active_chats[chat_id]['current_track'] = {
            'title': title,
            'url': audio_url,
            'start_time': time.time(),
            'duration': duration or 180,  # Default to 3 minutes if unknown
            'notify_callback': notify_callback,
            'progress_callback': progress_callback,
            'is_downloading': is_downloading,
            'partial_file': partial_file,
            'download_progress': download_progress,
            'thumbnail': song_info.get('thumbnail')  # Store thumbnail for menu integration
        }

        # Log that we've set the current track
        logger.info(f"Set current_track for chat {chat_id} to '{title}'")

        # Start queue worker if not running
        from asyncio import create_task
        if chat_id not in queue_workers or queue_workers[chat_id].done():
            logger.info(f"Starting queue worker for chat {chat_id}")
            queue_workers[chat_id] = create_task(queue_worker(chat_id))

        # Send notification BEFORE playing to ensure it stays visible
        # This fixes the issue where notifications disappear when processing message goes away
        status_msg = f"🎵 Now playing: {title}"
        if notify_callback:
            try:
                await notify_callback(status_msg)
                logger.info(f"Sent 'Now playing' notification for: {title}")
            except Exception as notify_err:
                logger.error(f"Failed to send notification: {notify_err}")

        # For PyTgCalls, play the audio
        try:
            # Resolve the actual file path with better error handling
            import os
            play_url = audio_url

            # If it's a local file path, validate and resolve it
            if not audio_url.startswith(('http://', 'https://')):
                # Try to find the actual file with various extensions
                def find_actual_file(base_path):
                    """Find the actual audio file, handling various extensions"""
                    possible_files = [
                        base_path,  # Original path
                        base_path + ".mp3",  # With .mp3 extension
                        base_path.replace('.mp3', '.mp3.mp3'),  # Double extension
                    ]

                    for file_path in possible_files:
                        if os.path.exists(file_path):
                            logger.info(f"Found actual audio file: {file_path}")
                            return os.path.abspath(file_path)
                    return None

                actual_file = find_actual_file(audio_url)
                if actual_file:
                    play_url = actual_file
                    logger.info(f"Resolved file path to: {play_url}")
                else:
                    # File not found, log available files for debugging
                    base_dir = os.path.dirname(audio_url)
                    if os.path.exists(base_dir):
                        available_files = os.listdir(base_dir)
                        logger.error(f"File not found: {audio_url}")
                        logger.error(f"Available files in {base_dir}: {available_files}")
                    else:
                        logger.error(f"Directory not found: {base_dir}")

                    # Reset current track on error
                    active_chats[chat_id]['current_track'] = None
                    return False, f"❌ Audio file not found: {os.path.basename(audio_url)}"

            logger.info(f"Playing {play_url} in chat {chat_id}")
            await py_tgcalls.play(chat_id, play_url)

            # Return success but don't send another notification
            # We've already sent it above
            return True, status_msg
        except Exception as e:
            logger.error(f"Error playing in voice chat: {e}")
            # Reset current track on error
            active_chats[chat_id]['current_track'] = None
            return False, f"❌ Error playing in voice chat: {e}"
    except Exception as e:
        logger.error(f"Error setting up voice chat: {e}")
        return False, f"❌ Error setting up voice chat: {e}"

async def pause_voice_chat(client, chat_id):
    """
    Pause the current playback in a voice chat
    """
    global py_tgcalls

    if py_tgcalls is None or chat_id not in active_chats:
        return False, "❌ Not currently in a voice chat"

    try:
        # For PyTgCalls 2.1.1, we use the pause method
        await py_tgcalls.pause(chat_id)
        return True, "⏸️ Playback paused. Use /resumevc to resume."
    except Exception as e:
        return False, f"❌ Error pausing playback: {str(e)}"

async def resume_voice_chat(client, chat_id):
    """
    Resume the paused playback in a voice chat
    """
    global py_tgcalls

    if py_tgcalls is None or chat_id not in active_chats:
        return False, "❌ Not currently in a voice chat"

    try:
        # For PyTgCalls 2.1.1, we use the resume method
        await py_tgcalls.resume(chat_id)
        return True, "▶️ Playback resumed"
    except Exception as e:
        return False, f"❌ Error resuming playback: {str(e)}"

async def stop_voice_chat(client, chat_id):
    """
    Stop the current playback in a voice chat
    """
    global py_tgcalls
    logger = logging.getLogger("musicbot.voice_chat")

    if py_tgcalls is None:
        logger.error("PyTgCalls not initialized")
        return False, "❌ Voice chat system not initialized"

    try:
        # Stop the current playback
        await py_tgcalls.stop(chat_id)

        # Clear current track
        if chat_id in active_chats:
            active_chats[chat_id]['current_track'] = None

        return True, "⏹️ Playback stopped"
    except Exception as e:
        logger.error(f"Error stopping voice chat: {e}")
        return False, f"❌ Error stopping voice chat: {e}"

async def force_next_song(client, chat_id):
    """
    Force the queue worker to play the next song immediately
    """
    global py_tgcalls
    logger = logging.getLogger("musicbot.voice_chat")

    if py_tgcalls is None:
        logger.error("PyTgCalls not initialized")
        return False, "❌ Voice chat system not initialized"

    try:
        # Check if there's a current track
        if chat_id not in active_chats or not active_chats[chat_id].get('current_track'):
            return False, "❌ Nothing is currently playing"

        # Check if there are songs in the queue
        if not active_chats[chat_id]['queue']:
            return False, "❌ No songs in the queue"

        # Stop the current playback
        await py_tgcalls.stop(chat_id)

        # Clear current track to trigger the queue worker to play the next song
        active_chats[chat_id]['current_track'] = None

        # Make sure the queue worker is running
        from asyncio import create_task
        if chat_id not in queue_workers or queue_workers[chat_id].done():
            logger.info(f"Starting queue worker for chat {chat_id}")
            queue_workers[chat_id] = create_task(queue_worker(chat_id))

        return True, "⏭️ Skipping to next song..."
    except Exception as e:
        logger.error(f"Error forcing next song: {e}")
        return False, f"❌ Error skipping to next song: {e}"

async def leave_voice_chat(client, chat_id):
    """
    Leave a voice chat
    """
    global py_tgcalls, queue_workers

    if py_tgcalls is None:
        return False, "❌ Voice chat system not initialized"

    try:
        # Check if we're in the voice chat
        if chat_id not in active_chats:
            return False, "❌ Not in the voice chat"

        # Leave the voice chat
        try:
            # For PyTgCalls 2.1.1, we use the leave_group_call method
            await py_tgcalls.leave_group_call(chat_id)

            # Clean up active_chats entry
            if chat_id in active_chats:
                del active_chats[chat_id]
            # Clean up queue worker
            if chat_id in queue_workers:
                queue_workers[chat_id].cancel()
                del queue_workers[chat_id]
            return True, "👋 Left the voice chat and cleaned up."
        except Exception as e:
            print(f"Error leaving voice chat: {str(e)}")
            # Clean up active_chats entry even if there was an error
            if chat_id in active_chats:
                del active_chats[chat_id]
            # Clean up queue worker
            if chat_id in queue_workers:
                queue_workers[chat_id].cancel()
                del queue_workers[chat_id]
            if chat_id in track_finished_events:
                del track_finished_events[chat_id]

            return False, f"Error leaving voice chat: {str(e)}"
    except Exception as e:
        return False, f"Error in leave_voice_chat: {str(e)}"
