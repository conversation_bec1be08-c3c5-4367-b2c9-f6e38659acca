"""
Queue worker logic for the Telegram Music Bot

This module contains the queue_worker background task, which monitors playback and auto-plays the next song in the queue.
"""
import os
import time
import asyncio
import mutagen
import logging

# These will be imported from voice_chat.py to avoid circular imports
# active_chats, py_tgcalls, queue_workers should be set by the main bot logic

async def queue_worker(chat_id):
    """
    Background task that monitors playback and auto-plays the next song in the queue.
    Uses song duration to trigger next playback, reports progress, and logs events.
    Enhanced with better error handling and recovery mechanisms.
    """
    # Import globals from voice_chat module
    from . import voice_chat

    logger = logging.getLogger("musicbot.queue")
    logger.info(f"[queue_worker] Started for chat_id={chat_id}")
    progress_last_reported = 0
    last_song_end_time = 0
    consecutive_errors = 0
    max_consecutive_errors = 3

    try:
        while True:
            await asyncio.sleep(0.5)

            # Get current state from voice_chat module
            active_chats = voice_chat.active_chats
            py_tgcalls = voice_chat.py_tgcalls
            queue_workers = voice_chat.queue_workers

            if chat_id not in active_chats:
                logger.info(f"[queue_worker] chat_id={chat_id} no longer active. Exiting worker.")
                break
            if py_tgcalls is None:
                logger.error(f"[queue_worker] PyTgCalls not initialized. Waiting...")
                await asyncio.sleep(2.0)
                continue
            current_track = active_chats[chat_id].get('current_track')
            current_time = time.time()
            if not current_track and active_chats[chat_id]['queue']:
                try:
                    if current_time - last_song_end_time < 1.5:
                        await asyncio.sleep(1.5)
                        continue
                    next_song = active_chats[chat_id]['queue'].pop(0)
                    logger.info(f"[queue_worker] Preparing to play next song: {next_song['title']}")
                    is_downloading = next_song.get('is_downloading', False)
                    partial_file = next_song.get('partial_file')
                    if is_downloading and partial_file:
                        from main import active_downloads
                        title = next_song['title']
                        if title in active_downloads:
                            download_info = active_downloads[title]
                            final_path = download_info.get('final_path')
                            if download_info.get('progress', 0) >= 99.5 and os.path.exists(final_path):
                                logger.info(f"[queue_worker] Download complete, using final file: {final_path}")
                                song_path = final_path
                            else:
                                song_path = partial_file
                                logger.info(f"[queue_worker] Using partial file: {partial_file} ({download_info.get('progress', 0):.1f}% complete)")
                        else:
                            song_path = partial_file
                    else:
                        song_path = next_song['url']

                    # Resolve the actual file path with better error handling
                    def find_actual_file(base_path):
                        """Find the actual audio file, handling various extensions"""
                        if base_path.startswith(('http://', 'https://')):
                            return base_path  # URL, return as-is

                        possible_files = [
                            base_path,  # Original path
                            base_path + ".mp3",  # With .mp3 extension
                            base_path.replace('.mp3', '.mp3.mp3'),  # Double extension
                        ]

                        for file_path in possible_files:
                            if os.path.exists(file_path):
                                logger.info(f"[queue_worker] Found actual audio file: {file_path}")
                                return os.path.abspath(file_path)
                        return None

                    actual_file = find_actual_file(song_path)
                    if actual_file:
                        song_path = actual_file
                        logger.info(f"[queue_worker] Resolved file path to: {song_path}")
                    elif not song_path.startswith(('http://', 'https://')):
                        # File not found, log available files for debugging
                        base_dir = os.path.dirname(song_path)
                        if os.path.exists(base_dir):
                            available_files = os.listdir(base_dir)
                            logger.error(f"[queue_worker] File not found: {song_path}")
                            logger.error(f"[queue_worker] Available files in {base_dir}: {available_files}")
                        else:
                            logger.error(f"[queue_worker] Directory not found: {base_dir}")

                        # Skip this song and continue with the next one
                        logger.error(f"[queue_worker] Skipping song due to missing file: {next_song['title']}")
                        consecutive_errors += 1
                        continue

                    # Play the song
                    await py_tgcalls.play(chat_id, song_path)
                    active_chats[chat_id]['current_track'] = next_song
                    last_song_end_time = time.time()
                    # Notify if needed
                    if next_song.get('notify_callback') and not next_song.get('_notification_sent'):
                        await next_song['notify_callback'](f"\U0001F3B5 Now playing: {next_song['title']}")
                        next_song['_notification_sent'] = True
                    continue
                except Exception as e:
                    logger.error(f"[queue_worker] Error playing next song: {e}")
                    consecutive_errors += 1
                    if consecutive_errors >= max_consecutive_errors:
                        logger.error(f"[queue_worker] Too many consecutive errors, stopping worker for chat_id={chat_id}")
                        break
                    await asyncio.sleep(2.0)
                    continue
            # If a track is playing, monitor duration and progress
            if current_track:
                try:
                    song_path = current_track.get('url')
                    duration = None
                    if os.path.isfile(song_path):
                        audio = mutagen.File(song_path)
                        if audio:
                            duration = int(audio.info.length)
                    start_time = current_track.get('start_time', time.time())
                    elapsed = time.time() - start_time
                    # --- Advanced detection and notification logic ---
                    song_mostly_done = duration and elapsed >= (duration * 0.8)
                    song_finished = duration and elapsed >= duration
                    song_stuck = duration and elapsed >= (duration * 1.5)

                    should_end_song = False
                    if song_stuck:
                        logger.warning(f"[queue_worker] Song appears to be stuck: {current_track['title']} in chat_id={chat_id} (elapsed: {elapsed}s, duration: {duration}s)")
                        should_end_song = True
                    elif song_finished:
                        logger.info(f"[queue_worker] Song finished normally: {current_track['title']} in chat_id={chat_id}")
                        should_end_song = True

                    if should_end_song:
                        # Only log to terminal, do not notify user in chat
                        logger.info(f"[queue_worker] Song finished (no user notification): {current_track['title']}")
                        # Try to force stop the current playback to ensure clean transition
                        try:
                            await py_tgcalls.stop(chat_id)
                            logger.info(f"[queue_worker] Successfully stopped playback for chat_id={chat_id}")
                        except Exception as stop_err:
                            logger.warning(f"[queue_worker] Failed to stop playback: {stop_err}")
                        # Mark the song as finished
                        active_chats[chat_id]['current_track'] = None
                        last_song_end_time = time.time()
                        continue
                except Exception as e:
                    logger.error(f"[queue_worker] Error monitoring track: {e}")
                    await asyncio.sleep(1.0)
                    continue
            # If queue is empty and nothing playing, cleanup after a delay
            try:
                if not active_chats[chat_id]['queue'] and not active_chats[chat_id].get('current_track'):
                    await asyncio.sleep(5.0)
                    if not active_chats[chat_id]['queue'] and not active_chats[chat_id].get('current_track'):
                        logger.info(f"[queue_worker] Queue empty and nothing playing in chat_id={chat_id}. Exiting worker.")
                        active_chats[chat_id]['current_track'] = None
                        break
            except Exception as e:
                logger.error(f"[queue_worker] Error checking queue status: {e}")
                await asyncio.sleep(1.0)
    except Exception as e:
        logger.error(f"[queue_worker] Fatal error: {e}")
