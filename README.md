<p align="center">
  <img src="https://user-images.githubusercontent.com/674621/235323123-7c7c7c7c-7c7c-4c7c-8c7c-7c7c7c7c7c7c.png" width="400" alt="Telegram Music Bot Logo">
</p>

<h1 align="center">🎧 Telegram Music Bot - Enhanced Edition 🎧</h1>

<p align="center">
  <b>Next-gen music bot for Telegram voice chats with instant playback, playlists, and a killer UX.</b><br>
  <a href="https://python.org/"><img src="https://img.shields.io/badge/Python-3.8%2B-blue?logo=python" alt="Python"></a>
  <a href="https://github.com/yourusername/tg-music-bot/blob/main/LICENSE"><img src="https://img.shields.io/github/license/yourusername/tg-music-bot.svg" alt="License"></a>
</p>

---

## 🚀 Features

- 🎵 **Voice Chat Music Playback**: Play music instantly in Telegram voice chats
- 🔄 **Multiple Sources**: YouTube, Spotify, and direct audio links supported
- 📝 **Advanced Queue System**: Seamless multi-song queue with auto-play
- 📂 **Playlist Management**: Create, edit, and share playlists with friends
- 🖼️ **Now Playing Thumbnails**: Beautiful now playing messages with cover art
- 💾 **Smart Caching**: Songs are cached for lightning-fast replays
- ⚡ **Immediate Play**: No waiting—songs start in seconds while downloading
- 🏗️ **Modern Python Stack**: Built with Hydrogram, Pyrogram, and Aiogram
- 🛡️ **Secure**: Sensitive data (tokens, sessions, downloads) is never committed

---

## 📦 Commands


### Basic Commands
- `/start` - Start the bot and show the main menu
- `/help` - Show help message with available commands
- `/play <song name or URL>` - Play a song in voice chat
- `/pause` - Pause the current playback
- `/resume` - Resume the paused playback
- `/skip` - Skip to the next song in queue
- `/queue` - Show current song queue
- `/clear` - Clear the song queue
- `/leave` - Leave the voice chat

### Playlist Commands
- `/createplaylist <name>` - Create a new playlist
- `/addtoplaylist <playlist> <song>` - Add a song to a playlist
- `/playlists` - Show your playlists
- `/playlist <name>` - Show songs in a playlist
- `/playplaylist <name>` - Play a playlist

## 🛠️ Requirements

- Python 3.8+
- FFmpeg (auto-downloaded)
- Telegram API credentials (API ID & API Hash)
- Bot token from [@BotFather](https://t.me/BotFather)
- User account session string (for voice chat)

---

## ⚡ Quickstart

```bash
git clone https://github.com/yourusername/tg-music-bot.git
cd tg-music-bot
pip install -r requirements.txt
python download_ffmpeg.py
```

1. Copy `config.py.example` to `config.py` and fill in your credentials:
   ```python
   API_ID = "your_api_id"
   API_HASH = "your_api_hash"
   BOT_TOKEN = "your_bot_token"
   SESSION_STRING = "your_user_session_string"
   ```
2. Run the bot:
   ```bash
   python main.py
   ```

---

## 🔒 Security & Privacy

- **Sensitive files are protected by `.gitignore`**: your API keys, session strings, downloaded songs, and playlists are never committed.
- **Never share your config or session files!**
- **If you fork or publish, always double-check your repo for secrets.**

---

## 🤝 Contributing

Pull requests and issues are welcome! Please:
- Use clear commit messages
- Follow Python best practices
- Respect the privacy and security model

---

## 📜 License

[MIT](LICENSE)

---

<p align="center">
  <b>Enjoy your music! 🎶<br>
  Made with ❤️ for the Telegram community.</b>
</p>

   - This will automatically download and set up FFmpeg for you!

   Or install FFmpeg manually:
   - **Windows**:
     1. Download from [ffmpeg.org](https://ffmpeg.org/download.html) (Windows Builds section)
     2. Extract the zip file to a location (e.g., `C:\ffmpeg`)
     3. Add the bin folder to your PATH
   - **Linux**: `sudo apt install ffmpeg`
   - **macOS**: `brew install ffmpeg`

4. Create a `.env` file with your credentials (optional):
   ```
   API_ID=your_api_id
   API_HASH=your_api_hash
   BOT_TOKEN=your_bot_token
   SESSION_STRING=your_session_string
   ```

5. Run the enhanced bot:
   ```bash
   python new_main.py
   ```

## Getting a Session String

To get a session string for the user account that will join voice chats:

1. Run the `generate_session.py` script:
```bash
python generate_session.py
```

2. Enter your phone number and the verification code when prompted
3. The session string will be displayed - copy it to your `.env` file or `config.py`

## Usage

### Basic Usage
1. Add the bot to a group
2. Start a voice chat in the group
3. Use `/play <song name or URL>` to play music directly in the voice chat
4. Use the inline buttons to control playback, manage queue, and access playlists

### Advanced Features
1. **Playlists**: Create and manage playlists with `/createplaylist` and related commands
2. **Queue Management**: View and manage the queue with `/queue` command
3. **Audio Effects**: Apply effects to audio (coming in future updates)
4. **Caching**: The bot automatically caches songs for faster playback

Note: The bot uses a user account (via session string) to join voice chats, as regular bot accounts cannot join voice chats in Telegram.

## Architecture

The bot uses a modern architecture with:

- **Hydrogram**: A modern fork of Pyrogram for the user client that joins voice chats
- **Aiogram**: A powerful asynchronous framework for the bot client
- **py-tgcalls**: For voice chat functionality
- **Redis/Local Cache**: For efficient caching of audio files and metadata
- **Modular Design**: Separate modules for audio processing, playlist management, voice chat, and caching

## Improvements Over Original Version

1. **Modern Libraries**: Upgraded from Pyrogram to Hydrogram and added Aiogram for better performance
2. **Advanced Caching**: Implemented Redis and local file caching for faster response times
3. **Better Audio Processing**: Enhanced audio quality and added effects
4. **Improved Error Handling**: Comprehensive error handling and recovery
5. **Environment Variable Support**: Added support for .env files and environment variables
6. **Code Organization**: Modular code structure for better maintainability
7. **Enhanced Playlist Management**: More robust playlist features with shuffle and repeat modes
8. **Better User Experience**: Improved inline keyboards and message formatting

## License

MIT

## Credits

- [Hydrogram](https://github.com/hydrogram/hydrogram) - Modern Telegram MTProto API framework
- [Aiogram](https://github.com/aiogram/aiogram) - Powerful asynchronous framework for Telegram Bot API
- [py-tgcalls](https://github.com/pytgcalls/pytgcalls) - Python library for Telegram calls
- [yt-dlp](https://github.com/yt-dlp/yt-dlp) - YouTube downloader
- [FFmpeg](https://ffmpeg.org/) - Audio processing library
