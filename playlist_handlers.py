"""
Enhanced playlist handlers for the Telegram Music Bot
"""
import os
import json
import logging
from pyrogram import filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from pyrogram.enums import ParseMode

from utils.playlist import (
    create_playlist, get_playlist, delete_playlist, 
    list_playlists, Playlist
)
from config import BASE_DIR

logger = logging.getLogger("musicbot.playlist_handlers")

# Import the song_hash_to_title from main.py
# This is defined here to avoid circular imports, but will be updated from main.py
song_hash_to_title = {}

async def create_playlist_command_handler(bot, message: Message):
    """Create a new playlist (robust command parsing)"""
    try:
        text = message.text or ""
        # Remove leading bot mention if present
        if text.startswith("@"):
            text = " ".join(text.split()[1:])
        # Remove the command itself (with or without bot mention)
        parts = text.split(maxsplit=1)
        if len(parts) < 2 or not parts[1].strip():
            await message.reply("❌ Please provide a valid name for the playlist: /createplaylist <name>")
            return
            
        playlist_name = parts[1].strip()
        user_id = message.from_user.id
        
        # Check if playlist already exists
        existing_playlist = await get_playlist(playlist_name, user_id)
        if existing_playlist:
            await message.reply(f"❌ Playlist '{playlist_name}' already exists.")
            return
            
        # Create the playlist
        new_playlist = await create_playlist(playlist_name, user_id)
        if new_playlist:
            await message.reply(f"✅ Playlist '{playlist_name}' created successfully.")
        else:
            await message.reply(f"❌ Failed to create playlist '{playlist_name}'.")
    except Exception as e:
        logger.error(f"Error in create playlist command: {e}")
        await message.reply(f"❌ An error occurred: {e}")

async def show_playlists_menu_handler(bot, chat_id, message=None):
    """Show the playlists menu"""
    try:
        user_id = message.from_user.id if message else chat_id
        
        # Get user's playlists
        playlists = await list_playlists(user_id)
        
        playlists_text = "📂 <b>Your Playlists:</b>\n\n" if playlists else "📂 <b>Your Playlists</b>\n\n<i>You don't have any playlists yet.</i>"
        
        # Show playlist details
        for name in playlists:
            try:
                playlist = await get_playlist(name, user_id)
                if playlist:
                    song_count = len(playlist.songs)
                    playlists_text += f"• <b>{name}</b> ({song_count} songs)\n"
                else:
                    playlists_text += f"• <b>{name}</b>\n"
            except Exception as e:
                logger.error(f"Error reading playlist {name}: {e}")
                playlists_text += f"• <b>{name}</b>\n"
        
        # Create buttons for each playlist with multiple actions
        buttons = []
        for name in playlists:
            buttons.append([
                InlineKeyboardButton(f"▶️ {name}", callback_data=f"play_playlist:{name}"),
                InlineKeyboardButton("❌", callback_data=f"delete_playlist:{name}")
            ])
        
        # Add create playlist button
        buttons.append([InlineKeyboardButton("➕ Create New Playlist", callback_data="create_playlist_prompt")])
        
        # Add back button
        buttons.append([InlineKeyboardButton("⬅️ Main Menu", callback_data="main_menu")])
        
        if message:
            await message.edit_text(playlists_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
        else:
            await bot.send_message(chat_id, playlists_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
    except Exception as e:
        logger.error(f"Error showing playlists menu: {e}")
        if message:
            await message.edit_text("❌ Failed to load playlists menu.")
        else:
            await bot.send_message(chat_id, "❌ Failed to load playlists menu.")

async def add_to_playlist_handler(bot, callback_query: CallbackQuery):
    """Handle adding a song to a playlist"""
    message = callback_query.message
    data = callback_query.data
    user_id = callback_query.from_user.id
    chat_id = message.chat.id
    
    song_hash = int(data.split("_", 1)[1])
    song_title = song_hash_to_title.get(song_hash)
    
    if not song_title:
        await message.reply("❌ Could not resolve song title for Add to Playlist.")
        await callback_query.answer()
        return
    
    # Get user's playlists
    playlists = await list_playlists(user_id)
    
    if not playlists:
        await message.reply("ℹ️ No playlists found. Use /createplaylist <name> to create one.")
        await callback_query.answer()
        return
    
    # Create buttons for each playlist
    buttons = []
    for name in playlists:
        buttons.append([InlineKeyboardButton(f"➕ {name}", callback_data=f"add_song_to_playlist:{name}:{song_hash}")])
    
    # Add back button
    buttons.append([InlineKeyboardButton("⬅️ Main Menu", callback_data="main_menu")])
    
    await message.reply(f"Select a playlist to add '{song_title}' to:", reply_markup=InlineKeyboardMarkup(buttons))
    await callback_query.answer()

async def add_song_to_playlist_handler(bot, callback_query: CallbackQuery):
    """Handle adding a song to a specific playlist"""
    message = callback_query.message
    data = callback_query.data
    user_id = callback_query.from_user.id
    
    _, playlist_name, song_hash_str = data.split(":", 2)
    song_hash = int(song_hash_str)
    song_title = song_hash_to_title.get(song_hash)
    
    if not song_title:
        await message.reply("❌ Could not resolve song title for Add to Playlist.")
        await callback_query.answer()
        return
    
    # Get the playlist
    playlist = await get_playlist(playlist_name, user_id)
    if not playlist:
        await message.reply(f"❌ Playlist '{playlist_name}' does not exist.")
        await callback_query.answer()
        return
    
    # Prepare the song info
    songs_dir = os.path.join(BASE_DIR, 'downloaded_songs')
    import re
    safe_title = re.sub(r'[^\w\-_\. ]', '_', song_title)
    song_file = os.path.join(songs_dir, f"{safe_title}.mp3")
    song_file_mp3mp3 = song_file + ".mp3"
    
    # Check if the song file exists
    if not (os.path.exists(song_file) or os.path.exists(song_file_mp3mp3)):
        # Check if a background download is in progress
        import time
        wait_time = 0
        while wait_time < 5 and not (os.path.exists(song_file) or os.path.exists(song_file_mp3mp3)):
            time.sleep(1)
            wait_time += 1
        
        if not (os.path.exists(song_file) or os.path.exists(song_file_mp3mp3)):
            await message.reply(f"❌ Song '{song_title}' not found in downloaded songs. Try playing it first to cache it.")
            await callback_query.answer()
            return
    
    # Use the file that exists
    song_path = song_file if os.path.exists(song_file) else song_file_mp3mp3
    
    # Create song info dictionary
    import time
    song_info = {
        'title': song_title,
        'path': song_path,
        'added_at': time.time()
    }
    
    # Check if song is already in playlist
    for existing_song in playlist.songs:
        if existing_song.get('title') == song_title or existing_song.get('path') in [song_file, song_file_mp3mp3]:
            await message.reply(f"ℹ️ Song already in playlist '{playlist_name}'.")
            await callback_query.answer()
            return
    
    # Add the song to the playlist
    success = await playlist.add_song(song_info)
    
    if success:
        await message.reply(f"✅ Added '{song_title}' to playlist '{playlist_name}'.")
    else:
        await message.reply(f"❌ Failed to add '{song_title}' to playlist '{playlist_name}'.")
    
    await callback_query.answer()

async def play_playlist_handler(bot, callback_query: CallbackQuery):
    """Handle playing a playlist"""
    message = callback_query.message
    data = callback_query.data
    user_id = callback_query.from_user.id
    chat_id = message.chat.id
    
    _, playlist_name = data.split(":", 1)
    
    # Get the playlist
    playlist = await get_playlist(playlist_name, user_id)
    if not playlist:
        await message.reply(f"❌ Playlist '{playlist_name}' does not exist.")
        await callback_query.answer()
        return
    
    # Check if playlist has songs
    if not playlist.songs:
        await message.reply(f"ℹ️ Playlist '{playlist_name}' is empty. Add songs first.")
        await callback_query.answer()
        return
    
    # Get the first song
    first_song = playlist.get_current_song()
    if not first_song:
        await message.reply(f"❌ Failed to get the first song from playlist '{playlist_name}'.")
        await callback_query.answer()
        return
    
    # Play the first song
    from player.voice_chat import play_in_voice_chat
    from main import user  # Get the user client
    
    # Create notification callback
    async def notify_callback(text):
        try:
            await message.reply(text)
        except Exception as e:
            logger.error(f"Error in notify callback: {e}")
    
    # Play the song
    success, play_message = await play_in_voice_chat(
        user, chat_id, first_song['path'], first_song['title'], notify_callback
    )
    
    if success:
        # Queue the rest of the songs
        from player.voice_chat import active_chats
        
        if chat_id in active_chats:
            # Add remaining songs to queue
            import time
            for i in range(1, len(playlist.songs)):
                song = playlist.songs[i]
                active_chats[chat_id]['queue'].append({
                    'title': song['title'],
                    'url': song['path'],
                    'notify_callback': notify_callback,
                    'progress_callback': None,
                    'added_at': time.time()
                })
            
            # Ensure queue worker is running
            from player.voice_chat import queue_workers, queue_worker
            from asyncio import create_task
            if chat_id not in queue_workers or queue_workers[chat_id].done():
                queue_workers[chat_id] = create_task(queue_worker(chat_id))
            
            await message.reply(f"✅ Playing playlist '{playlist_name}' ({len(playlist.songs)} songs)")
        else:
            await message.reply(f"✅ Playing first song from playlist '{playlist_name}'")
    else:
        await message.reply(f"❌ Failed to play playlist: {play_message}")
    
    await callback_query.answer()

async def delete_playlist_handler(bot, callback_query: CallbackQuery):
    """Handle deleting a playlist"""
    message = callback_query.message
    data = callback_query.data
    user_id = callback_query.from_user.id
    
    _, playlist_name = data.split(":", 1)
    
    # Confirm deletion
    buttons = [
        [
            InlineKeyboardButton("✅ Yes, delete it", callback_data=f"confirm_delete_playlist:{playlist_name}"),
            InlineKeyboardButton("❌ No, keep it", callback_data="show_playlists")
        ]
    ]
    
    await message.edit_text(
        f"Are you sure you want to delete playlist '{playlist_name}'?",
        reply_markup=InlineKeyboardMarkup(buttons)
    )
    
    await callback_query.answer()

async def confirm_delete_playlist_handler(bot, callback_query: CallbackQuery):
    """Handle confirming playlist deletion"""
    message = callback_query.message
    data = callback_query.data
    user_id = callback_query.from_user.id
    
    _, playlist_name = data.split(":", 1)
    
    # Delete the playlist
    success = await delete_playlist(playlist_name, user_id)
    
    if success:
        await message.edit_text(f"✅ Playlist '{playlist_name}' has been deleted.")
    else:
        await message.edit_text(f"❌ Failed to delete playlist '{playlist_name}'.")
    
    # Show playlists menu after a short delay
    import asyncio
    await asyncio.sleep(2)
    await show_playlists_menu_handler(bot, user_id, message)
    
    await callback_query.answer()

def register_playlist_handlers(bot):
    """Register all playlist handlers with the bot"""
    # Command handlers
    bot.on_message(filters.command("createplaylist"))(
        lambda _, message: create_playlist_command_handler(bot, message)
    )
    
    # Callback query handlers
    @bot.on_callback_query(filters.regex("^addpl_"))
    async def add_to_playlist_callback(_, callback_query):
        await add_to_playlist_handler(bot, callback_query)
    
    @bot.on_callback_query(filters.regex("^add_song_to_playlist:"))
    async def add_song_to_playlist_callback(_, callback_query):
        await add_song_to_playlist_handler(bot, callback_query)
    
    @bot.on_callback_query(filters.regex("^play_playlist:"))
    async def play_playlist_callback(_, callback_query):
        await play_playlist_handler(bot, callback_query)
    
    @bot.on_callback_query(filters.regex("^delete_playlist:"))
    async def delete_playlist_callback(_, callback_query):
        await delete_playlist_handler(bot, callback_query)
    
    @bot.on_callback_query(filters.regex("^confirm_delete_playlist:"))
    async def confirm_delete_playlist_callback(_, callback_query):
        await confirm_delete_playlist_handler(bot, callback_query)
    
    @bot.on_callback_query(filters.regex("^show_playlists$"))
    async def show_playlists_callback(_, callback_query):
        await show_playlists_menu_handler(bot, callback_query.message.chat.id, callback_query.message)
        await callback_query.answer()
    
    @bot.on_callback_query(filters.regex("^create_playlist_prompt$"))
    async def create_playlist_prompt_callback(_, callback_query):
        await callback_query.message.reply("Please use the /createplaylist command followed by the playlist name to create a new playlist.")
        await callback_query.answer()