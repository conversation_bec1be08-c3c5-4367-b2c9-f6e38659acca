"""
Advanced audio processing utilities for the Telegram Music Bot
"""
import os
import re
import asyncio
import logging
import tempfile
from typing import Dict, Any, Optional, Tuple, List
import aiofiles
import aiohttp
from pydub import AudioSegment
import yt_dlp

from config import AUDIO_CACHE_DIR, DEFAULT_AUDIO_QUALITY, FFMPEG_PATH, FFPROBE_PATH
from utils.cache import cache_audio_metadata, get_cached_audio_metadata, is_audio_file_cached, get_cached_audio_path

logger = logging.getLogger("musicbot.audio")

# Ensure FFmpeg paths are set
os.environ["FFMPEG_BINARY"] = FFMPEG_PATH
os.environ["FFPROBE_BINARY"] = FFPROBE_PATH

# Configure yt-dlp with better options
YDL_OPTIONS = {
    'format': 'bestaudio/best',
    'extractaudio': True,
    'audioformat': 'mp3',
    'outtmpl': '%(title)s.%(ext)s',
    'restrictfilenames': True,
    'noplaylist': True,
    'nocheckcertificate': True,
    'ignoreerrors': False,
    'logtostderr': False,
    'quiet': True,
    'no_warnings': True,
    'default_search': 'auto',
    'source_address': '0.0.0.0',
    'postprocessors': [{
        'key': 'FFmpegExtractAudio',
        'preferredcodec': 'mp3',
        'preferredquality': DEFAULT_AUDIO_QUALITY,
    }],
}

def is_youtube_url(url: str) -> bool:
    """Check if a URL is a YouTube URL"""
    youtube_regex = r'(https?://)?(www\.)?(youtube|youtu|youtube-nocookie)\.(com|be)/(watch\?v=|embed/|v/|.+\?v=)?([^&=%\?]{11})'
    return bool(re.match(youtube_regex, url))

def is_spotify_url(url: str) -> bool:
    """Check if a URL is a Spotify URL"""
    spotify_regex = r'(https?://)?(open\.)?spotify\.(com)/track/[a-zA-Z0-9]+'
    return bool(re.match(spotify_regex, url))

def is_direct_audio_url(url: str) -> bool:
    """Check if a URL is a direct audio file URL"""
    audio_extensions = ['.mp3', '.m4a', '.wav', '.flac', '.aac', '.ogg']
    return any(url.lower().endswith(ext) for ext in audio_extensions)

def sanitize_filename(title: str) -> str:
    """Sanitize a title to be used as a filename"""
    # Replace invalid characters with underscore
    safe_title = re.sub(r'[^\w\-_\. ]', '_', title)
    # Limit length for Windows
    return safe_title[:64]

async def get_youtube_url_from_query(query: str) -> Optional[str]:
    """Search YouTube and get the first result URL"""
    try:
        with yt_dlp.YoutubeDL({'quiet': True, 'format': 'bestaudio', 'noplaylist': True}) as ydl:
            result = ydl.extract_info(f"ytsearch1:{query}", download=False)
            if 'entries' in result and result['entries']:
                return f"https://www.youtube.com/watch?v={result['entries'][0]['id']}"
    except Exception as e:
        logger.error(f"Error searching YouTube: {e}")
    return None

async def extract_spotify_track_info(url: str) -> Optional[str]:
    """Extract track name from Spotify URL and return a search query"""
    try:
        # This is a simplified version, a real implementation would use Spotify API
        track_id = url.split('/')[-1].split('?')[0]
        
        # Try to get track info from Spotify API (would require API key)
        # For now, just extract from URL
        track_name = url.split('/')[-1].replace('-', ' ')
        return track_name
    except Exception as e:
        logger.error(f"Error extracting Spotify track info: {e}")
        return None

async def get_audio_stream(url_or_query: str) -> Optional[Dict[str, Any]]:
    """
    Get audio stream info from URL or query
    Returns a dict with stream_url, title, duration, and thumbnail
    """
    # Check if we have cached metadata
    cached_metadata = await get_cached_audio_metadata(url_or_query)
    if cached_metadata:
        logger.info(f"Using cached metadata for: {url_or_query}")
        return cached_metadata

    # Process the URL or query
    try:
        # If it's a direct audio URL, return it directly
        if is_direct_audio_url(url_or_query):
            filename = os.path.basename(url_or_query)
            return {
                'stream_url': url_or_query,
                'title': filename,
                'duration': 0,
                'thumbnail': None
            }

        # If it's not a YouTube URL, try to search for it
        if not is_youtube_url(url_or_query):
            if is_spotify_url(url_or_query):
                # Extract track name from Spotify and search on YouTube
                track_name = await extract_spotify_track_info(url_or_query)
                if track_name:
                    url_or_query = await get_youtube_url_from_query(track_name)
            else:
                # Treat as a search query
                url_or_query = await get_youtube_url_from_query(url_or_query)

        if not url_or_query:
            return None

        # Extract audio URL from YouTube
        with yt_dlp.YoutubeDL(YDL_OPTIONS) as ydl:
            info = ydl.extract_info(url_or_query, download=False)
            metadata = {
                'stream_url': info.get('url'),
                'title': info.get('title', 'Unknown Title'),
                'duration': info.get('duration', 0),
                'thumbnail': info.get('thumbnail', None),
                'artist': info.get('artist', None),
                'uploader': info.get('uploader', None),
                'webpage_url': info.get('webpage_url', url_or_query)
            }
            
            # Cache the metadata
            await cache_audio_metadata(url_or_query, metadata)
            
            return metadata
    except Exception as e:
        logger.error(f"Error extracting audio stream: {e}")
        return None

async def download_audio(url_or_query: str) -> Tuple[bool, str, Optional[str]]:
    """
    Download audio from URL or query
    Returns (success, title, file_path)
    """
    try:
        # Get stream info
        stream_info = await get_audio_stream(url_or_query)
        if not stream_info:
            logger.error(f"No stream info for: {url_or_query}")
            return False, "Could not find audio source", None

        stream_url = stream_info['stream_url']
        title = stream_info['title']

        # Check if already cached
        if await is_audio_file_cached(title):
            audio_path = await get_cached_audio_path(title)
            logger.info(f"Using cached audio file: {audio_path}")
            return True, title, audio_path

        # Sanitize filename
        safe_title = sanitize_filename(title)
        audio_file = os.path.join(AUDIO_CACHE_DIR, f"{safe_title}.mp3")

        logger.info(f"Downloading: {title} -> {audio_file}")
        
        # Configure yt-dlp for this download
        ydl_opts = {
            'format': 'bestaudio/best',
            'outtmpl': audio_file,
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': DEFAULT_AUDIO_QUALITY,
            }],
            'quiet': True,
        }
        
        # Download the file
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([stream_url if is_direct_audio_url(stream_url) else stream_info['webpage_url']])

        if not os.path.isfile(audio_file):
            logger.error(f"File not found after download: {audio_file}")
            return False, "Download failed: file not found", None

        logger.info(f"Download success: {audio_file}")
        return True, title, audio_file
    except Exception as e:
        logger.error(f"Error downloading audio: {e}")
        return False, f"Error downloading audio: {str(e)}", None

async def apply_audio_effects(audio_file: str, effects: Dict[str, Any]) -> Optional[str]:
    """
    Apply audio effects to a file
    Returns path to the processed file
    """
    try:
        # Load the audio file
        audio = AudioSegment.from_file(audio_file)
        
        # Apply volume adjustment if specified
        if 'volume' in effects:
            audio = audio + effects['volume']  # volume in dB
            
        # Apply fade in/out if specified
        if 'fade_in' in effects:
            audio = audio.fade_in(effects['fade_in'] * 1000)  # convert to ms
            
        if 'fade_out' in effects:
            audio = audio.fade_out(effects['fade_out'] * 1000)  # convert to ms
            
        # Apply speed/tempo change if specified (requires ffmpeg with rubberband)
        if 'speed' in effects and effects['speed'] != 1.0:
            # Create a temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
            temp_file.close()
            
            # Export the audio to the temp file
            audio.export(temp_file.name, format="mp3")
            
            # Apply speed change using ffmpeg
            output_file = f"{audio_file}_processed.mp3"
            speed = effects['speed']
            
            # Build the ffmpeg command
            cmd = [
                FFMPEG_PATH,
                '-i', temp_file.name,
                '-filter:a', f'atempo={speed}',
                '-y',  # Overwrite output file if it exists
                output_file
            ]
            
            # Run the command
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            # Clean up the temp file
            os.unlink(temp_file.name)
            
            return output_file
            
        # If no speed change, just export with other effects
        output_file = f"{audio_file}_processed.mp3"
        audio.export(output_file, format="mp3")
        return output_file
        
    except Exception as e:
        logger.error(f"Error applying audio effects: {e}")
        return None
