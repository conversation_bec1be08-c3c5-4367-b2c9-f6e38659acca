"""
Lyrics Service - Get song lyrics from various sources
"""

import asyncio
import aiohttp
from typing import Dict, Optional
from loguru import logger
import re
from lyricsgenius import Genius

class LyricsService:
    """Service for fetching song lyrics"""
    
    def __init__(self, genius_api_key: str = None):
        """Initialize lyrics service"""
        self.genius = None
        self.enabled = False
        
        if genius_api_key:
            try:
                self.genius = Genius(genius_api_key, verbose=False, retries=3)
                self.enabled = True
                logger.info("Lyrics service initialized with Genius API")
            except Exception as e:
                logger.error(f"Failed to initialize Genius API: {e}")
        
        # Alternative lyrics sources
        self.alternative_sources = [
            self._get_lyrics_ovh,
            self._get_azlyrics,
            self._get_musixmatch_unofficial
        ]
    
    async def get_lyrics(self, artist: str, title: str) -> Optional[Dict]:
        """Get lyrics for a song"""
        # Clean the search query
        artist = self._clean_query(artist)
        title = self._clean_query(title)
        
        # Try Genius API first
        if self.genius and self.enabled:
            lyrics = await self._get_genius_lyrics(artist, title)
            if lyrics:
                return lyrics
        
        # Try alternative sources
        for source in self.alternative_sources:
            try:
                lyrics = await source(artist, title)
                if lyrics:
                    return lyrics
            except Exception as e:
                logger.debug(f"Error with lyrics source: {e}")
                continue
        
        return None
    
    async def _get_genius_lyrics(self, artist: str, title: str) -> Optional[Dict]:
        """Get lyrics from Genius API"""
        try:
            # Search for the song
            song = await asyncio.to_thread(
                self.genius.search_song,
                title,
                artist,
                get_full_info=False
            )
            
            if song and song.lyrics:
                return {
                    "lyrics": song.lyrics,
                    "title": song.title,
                    "artist": song.artist,
                    "url": song.url,
                    "source": "Genius",
                    "album": song.album,
                    "year": song.year,
                    "thumbnail": song.song_art_image_url
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting Genius lyrics: {e}")
            return None
    
    async def _get_lyrics_ovh(self, artist: str, title: str) -> Optional[Dict]:
        """Get lyrics from lyrics.ovh API"""
        try:
            url = f"https://api.lyrics.ovh/v1/{artist}/{title}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'lyrics' in data:
                            return {
                                "lyrics": data['lyrics'],
                                "title": title,
                                "artist": artist,
                                "source": "Lyrics.ovh"
                            }
            
            return None
            
        except Exception as e:
            logger.debug(f"Error with lyrics.ovh: {e}")
            return None
    
    async def _get_azlyrics(self, artist: str, title: str) -> Optional[Dict]:
        """Get lyrics from AZLyrics (unofficial)"""
        try:
            # Format for AZLyrics URL
            artist_formatted = re.sub(r'[^a-z0-9]', '', artist.lower())
            title_formatted = re.sub(r'[^a-z0-9]', '', title.lower())
            
            url = f"https://www.azlyrics.com/lyrics/{artist_formatted}/{title_formatted}.html"
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        html = await response.text()
                        
                        # Extract lyrics from HTML
                        lyrics_match = re.search(
                            r'<!-- Usage of azlyrics\.com content.*?-->(.*?)<!-- MxM banner -->',
                            html,
                            re.DOTALL
                        )
                        
                        if lyrics_match:
                            lyrics = lyrics_match.group(1)
                            # Clean up the lyrics
                            lyrics = re.sub(r'<[^>]+>', '', lyrics)
                            lyrics = lyrics.strip()
                            
                            if lyrics:
                                return {
                                    "lyrics": lyrics,
                                    "title": title,
                                    "artist": artist,
                                    "source": "AZLyrics"
                                }
            
            return None
            
        except Exception as e:
            logger.debug(f"Error with AZLyrics: {e}")
            return None
    
    async def _get_musixmatch_unofficial(self, artist: str, title: str) -> Optional[Dict]:
        """Get lyrics from Musixmatch (unofficial API)"""
        try:
            search_url = "https://apic-desktop.musixmatch.com/ws/1.1/track.search"
            params = {
                'q_track': title,
                'q_artist': artist,
                'page_size': 1,
                'f_has_lyrics': 1,
                'app_id': 'web-desktop-app-v1.0'
            }
            
            async with aiohttp.ClientSession() as session:
                # Search for track
                async with session.get(search_url, params=params, timeout=10) as response:
                    if response.status != 200:
                        return None
                    
                    data = await response.json()
                    track_list = data.get('message', {}).get('body', {}).get('track_list', [])
                    
                    if not track_list:
                        return None
                    
                    track_id = track_list[0]['track']['track_id']
                
                # Get lyrics
                lyrics_url = "https://apic-desktop.musixmatch.com/ws/1.1/track.lyrics.get"
                params = {
                    'track_id': track_id,
                    'app_id': 'web-desktop-app-v1.0'
                }
                
                async with session.get(lyrics_url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        lyrics_data = data.get('message', {}).get('body', {}).get('lyrics', {})
                        
                        if lyrics_data and lyrics_data.get('lyrics_body'):
                            return {
                                "lyrics": lyrics_data['lyrics_body'],
                                "title": title,
                                "artist": artist,
                                "source": "Musixmatch",
                                "copyright": lyrics_data.get('lyrics_copyright', '')
                            }
            
            return None
            
        except Exception as e:
            logger.debug(f"Error with Musixmatch: {e}")
            return None
    
    async def search_lyrics(self, query: str) -> Optional[Dict]:
        """Search for lyrics with a general query"""
        # Try to extract artist and title from query
        parts = query.split(' - ', 1)
        if len(parts) == 2:
            artist, title = parts
        else:
            # Try common patterns
            for pattern in [' by ', ' - ', '/', '|']:
                if pattern in query:
                    parts = query.split(pattern, 1)
                    if len(parts) == 2:
                        title, artist = parts
                        break
            else:
                # Use the whole query as title
                artist = ""
                title = query
        
        return await self.get_lyrics(artist, title)
    
    def _clean_query(self, text: str) -> str:
        """Clean query for better search results"""
        # Remove common additions
        text = re.sub(r'\s*\([^)]*\)', '', text)  # Remove parentheses
        text = re.sub(r'\s*\[[^\]]*\]', '', text)  # Remove brackets
        text = re.sub(r'\s*-\s*(Official|Video|Audio|Lyrics|HD|HQ|Live).*$', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\s*ft\.?\s*.*$', '', text, flags=re.IGNORECASE)  # Remove featuring
        text = re.sub(r'\s*feat\.?\s*.*$', '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def format_lyrics_message(self, lyrics_data: Dict, max_length: int = 4000) -> str:
        """Format lyrics for Telegram message"""
        lyrics = lyrics_data.get('lyrics', 'Lyrics not found')
        
        # Format the message
        message = f"🎵 **{lyrics_data.get('title', 'Unknown')}**\n"
        message += f"👤 {lyrics_data.get('artist', 'Unknown Artist')}\n"
        
        if lyrics_data.get('album'):
            message += f"💿 {lyrics_data['album']}\n"
        
        message += f"📝 Source: {lyrics_data.get('source', 'Unknown')}\n\n"
        
        # Add lyrics (truncate if too long)
        if len(lyrics) + len(message) > max_length:
            available_length = max_length - len(message) - 20
            lyrics = lyrics[:available_length] + "...\n\n[Truncated]"
        
        message += lyrics
        
        # Add copyright if available
        if lyrics_data.get('copyright'):
            message += f"\n\n© {lyrics_data['copyright']}"
        
        return message
