"""
Enhanced player control handlers for the Telegram Music Bot
Separated from main.py for better code organization and separation of concerns
"""
import os
import logging
import time
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from pyrogram.enums import ParseMode

logger = logging.getLogger("musicbot.player_controls")

async def create_enhanced_now_playing_message(bot, chat_id, title, thumbnail=None, duration=None, elapsed=0):
    """Create an enhanced now playing message with comprehensive inline controls"""
    try:
        # Format time display
        time_str = ""
        if duration and duration > 0:
            elapsed_mins, elapsed_secs = divmod(int(elapsed), 60)
            total_mins, total_secs = divmod(int(duration), 60)
            time_str = f"\n⏱️ {elapsed_mins}:{elapsed_secs:02d} / {total_mins}:{total_secs:02d}"
        
        # Create message text
        message_text = f"🎵 <b>Now Playing:</b> {title}{time_str}"
        
        # Create comprehensive inline controls
        buttons = [
            # Primary playback controls
            [
                InlineKeyboardButton("⏮️", callback_data="prev_song"),
                InlineKeyboardButton("⏸️", callback_data="pause_playback"),
                InlineKeyboardButton("⏯️", callback_data="resume_playback"),
                InlineKeyboardButton("⏭️", callback_data="skip_song")
            ],
            # Secondary controls
            [
                InlineKeyboardButton("🔄", callback_data="refresh_now_playing"),
                InlineKeyboardButton("🎶", callback_data="show_queue"),
                InlineKeyboardButton("🔀", callback_data="shuffle_queue"),
                InlineKeyboardButton("🔁", callback_data="toggle_repeat")
            ],
            # Volume and settings
            [
                InlineKeyboardButton("🔊", callback_data="volume_controls"),
                InlineKeyboardButton("⚙️", callback_data="player_settings"),
                InlineKeyboardButton("📋", callback_data="main_menu")
            ]
        ]
        
        keyboard = InlineKeyboardMarkup(buttons)
        
        # Send with thumbnail if available
        if thumbnail:
            try:
                return await bot.send_photo(
                    chat_id=chat_id,
                    photo=thumbnail,
                    caption=message_text,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.HTML
                )
            except Exception as e:
                logger.warning(f"Failed to send photo, falling back to text: {e}")
        
        # Fallback to text message
        return await bot.send_message(
            chat_id=chat_id,
            text=message_text,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )
        
    except Exception as e:
        logger.error(f"Error creating now playing message: {e}")
        return None

async def handle_pause_playback(bot, callback_query: CallbackQuery):
    """Handle pause playback button"""
    try:
        from player.voice_chat import pause_voice_chat
        from main import user  # Get the user client
        
        chat_id = callback_query.message.chat.id
        success, result = await pause_voice_chat(user, chat_id)
        
        if success:
            # Update button to show resume
            await update_playback_controls(bot, callback_query.message, paused=True)
            await callback_query.answer("⏸️ Playback paused")
        else:
            await callback_query.answer(f"❌ {result}")
            
    except Exception as e:
        logger.error(f"Error handling pause: {e}")
        await callback_query.answer("❌ Error pausing playback")

async def handle_resume_playback(bot, callback_query: CallbackQuery):
    """Handle resume playback button"""
    try:
        from player.voice_chat import resume_voice_chat
        from main import user  # Get the user client
        
        chat_id = callback_query.message.chat.id
        success, result = await resume_voice_chat(user, chat_id)
        
        if success:
            # Update button to show pause
            await update_playback_controls(bot, callback_query.message, paused=False)
            await callback_query.answer("▶️ Playback resumed")
        else:
            await callback_query.answer(f"❌ {result}")
            
    except Exception as e:
        logger.error(f"Error handling resume: {e}")
        await callback_query.answer("❌ Error resuming playback")

async def handle_skip_song(bot, callback_query: CallbackQuery):
    """Handle skip song button"""
    try:
        from player.voice_chat import skip_current_song
        from main import user  # Get the user client
        
        chat_id = callback_query.message.chat.id
        success, result = await skip_current_song(user, chat_id)
        
        if success:
            await callback_query.answer("⏭️ Skipped to next song")
            # Refresh the now playing message
            await handle_refresh_now_playing(bot, callback_query)
        else:
            await callback_query.answer(f"❌ {result}")
            
    except Exception as e:
        logger.error(f"Error handling skip: {e}")
        await callback_query.answer("❌ Error skipping song")

async def handle_previous_song(bot, callback_query: CallbackQuery):
    """Handle previous song button"""
    try:
        from player.voice_chat import previous_song
        from main import user  # Get the user client
        
        chat_id = callback_query.message.chat.id
        # This functionality needs to be implemented in voice_chat.py
        success, result = await previous_song(user, chat_id)
        
        if success:
            await callback_query.answer("⏮️ Playing previous song")
            # Refresh the now playing message
            await handle_refresh_now_playing(bot, callback_query)
        else:
            await callback_query.answer(f"❌ {result}")
            
    except Exception as e:
        logger.error(f"Error handling previous song: {e}")
        await callback_query.answer("❌ Previous song not available")

async def handle_refresh_now_playing(bot, callback_query: CallbackQuery):
    """Handle refresh now playing button"""
    try:
        from player.voice_chat import active_chats, is_playing
        
        chat_id = callback_query.message.chat.id
        is_currently_playing = await is_playing(chat_id)
        current_track = None
        
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            current_track = active_chats[chat_id]['current_track']
            
        if current_track and is_currently_playing:
            # Calculate elapsed time
            elapsed = time.time() - current_track.get('start_time', 0)
            duration = current_track.get('duration', 0)
            
            # Update the message with current info
            await update_now_playing_message(
                bot, callback_query.message, 
                current_track['title'], 
                duration=duration, 
                elapsed=elapsed
            )
            await callback_query.answer("🔄 Refreshed")
        else:
            await callback_query.answer("⏹️ Nothing playing")
            
    except Exception as e:
        logger.error(f"Error refreshing now playing: {e}")
        await callback_query.answer("❌ Error refreshing")

async def update_playback_controls(bot, message, paused=False):
    """Update the playback control buttons based on current state"""
    try:
        from player.voice_chat import active_chats
        
        chat_id = message.chat.id
        current_track = None
        
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            current_track = active_chats[chat_id]['current_track']
        
        if current_track:
            # Create buttons based on current state
            play_pause_button = "▶️" if paused else "⏸️"
            play_pause_data = "resume_playback" if paused else "pause_playback"
            
            buttons = [
                # Primary playback controls
                [
                    InlineKeyboardButton("⏮️", callback_data="prev_song"),
                    InlineKeyboardButton(play_pause_button, callback_data=play_pause_data),
                    InlineKeyboardButton("⏭️", callback_data="skip_song")
                ],
                # Secondary controls
                [
                    InlineKeyboardButton("🔄", callback_data="refresh_now_playing"),
                    InlineKeyboardButton("🎶", callback_data="show_queue"),
                    InlineKeyboardButton("🔀", callback_data="shuffle_queue")
                ],
                # Settings
                [
                    InlineKeyboardButton("🔊", callback_data="volume_controls"),
                    InlineKeyboardButton("📋", callback_data="main_menu")
                ]
            ]
            
            keyboard = InlineKeyboardMarkup(buttons)
            await message.edit_reply_markup(reply_markup=keyboard)
            
    except Exception as e:
        logger.error(f"Error updating playback controls: {e}")

async def update_now_playing_message(bot, message, title, duration=None, elapsed=0):
    """Update the now playing message with current information"""
    try:
        # Format time display
        time_str = ""
        if duration and duration > 0:
            elapsed_mins, elapsed_secs = divmod(int(elapsed), 60)
            total_mins, total_secs = divmod(int(duration), 60)
            time_str = f"\n⏱️ {elapsed_mins}:{elapsed_secs:02d} / {total_mins}:{total_secs:02d}"
        
        # Create message text
        message_text = f"🎵 <b>Now Playing:</b> {title}{time_str}"
        
        # Update the message text
        await message.edit_caption(caption=message_text, parse_mode=ParseMode.HTML)
        
    except Exception as e:
        logger.error(f"Error updating now playing message: {e}")
        # Fallback to edit_text if edit_caption fails
        try:
            await message.edit_text(text=message_text, parse_mode=ParseMode.HTML)
        except:
            pass
