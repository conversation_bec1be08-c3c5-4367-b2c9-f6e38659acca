"""
Optimized Queue Manager for Telegram Music Bot
Provides efficient queue operations with caching and performance optimizations
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from functools import lru_cache
from dataclasses import dataclass, asdict
from collections import deque

logger = logging.getLogger("musicbot.queue_manager")

@dataclass
class QueueItem:
    """Optimized queue item with essential data"""
    title: str
    url: str
    duration: int = 0
    thumbnail: Optional[str] = None
    added_at: float = 0
    notify_callback: Optional[Any] = None
    progress_callback: Optional[Any] = None
    is_downloading: bool = False
    partial_file: Optional[str] = None
    download_progress: float = 0
    
    def __post_init__(self):
        if self.added_at == 0:
            self.added_at = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for compatibility"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QueueItem':
        """Create from dictionary"""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

class OptimizedQueue:
    """High-performance queue implementation with caching"""
    
    def __init__(self, max_size: int = 100):
        self._items: deque = deque(maxlen=max_size)
        self._cache_dirty = True
        self._cached_list = []
        self._max_size = max_size
    
    def add(self, item: QueueItem) -> int:
        """Add item to queue and return position"""
        self._items.append(item)
        self._cache_dirty = True
        return len(self._items)
    
    def remove(self, index: int) -> Optional[QueueItem]:
        """Remove item by index (0-based)"""
        if 0 <= index < len(self._items):
            # Convert deque to list for index-based removal
            items_list = list(self._items)
            removed = items_list.pop(index)
            self._items = deque(items_list, maxlen=self._max_size)
            self._cache_dirty = True
            return removed
        return None
    
    def pop(self) -> Optional[QueueItem]:
        """Remove and return first item"""
        if self._items:
            self._cache_dirty = True
            return self._items.popleft()
        return None
    
    def peek(self) -> Optional[QueueItem]:
        """Get first item without removing"""
        return self._items[0] if self._items else None
    
    def clear(self):
        """Clear all items"""
        self._items.clear()
        self._cache_dirty = True
    
    def get_items(self, limit: Optional[int] = None) -> List[QueueItem]:
        """Get queue items with caching"""
        if self._cache_dirty:
            self._cached_list = list(self._items)
            self._cache_dirty = False
        
        if limit:
            return self._cached_list[:limit]
        return self._cached_list.copy()
    
    def __len__(self) -> int:
        return len(self._items)
    
    def __bool__(self) -> bool:
        return bool(self._items)

class QueueManager:
    """Centralized queue management with optimizations"""
    
    def __init__(self):
        self._queues: Dict[int, OptimizedQueue] = {}
        self._current_tracks: Dict[int, Optional[QueueItem]] = {}
        self._queue_workers: Dict[int, asyncio.Task] = {}
        self._last_activity: Dict[int, float] = {}
        
        # Performance metrics
        self._metrics = {
            'queue_operations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'worker_restarts': 0
        }
    
    def get_queue(self, chat_id: int) -> OptimizedQueue:
        """Get or create queue for chat"""
        if chat_id not in self._queues:
            self._queues[chat_id] = OptimizedQueue()
            self._current_tracks[chat_id] = None
            self._last_activity[chat_id] = time.time()
        
        self._last_activity[chat_id] = time.time()
        return self._queues[chat_id]
    
    def add_to_queue(self, chat_id: int, song_data: Dict[str, Any]) -> Tuple[int, QueueItem]:
        """Add song to queue and return position and item"""
        queue = self.get_queue(chat_id)
        item = QueueItem.from_dict(song_data)
        position = queue.add(item)
        
        self._metrics['queue_operations'] += 1
        logger.debug(f"Added '{item.title}' to queue at position {position} for chat {chat_id}")
        
        return position, item
    
    def remove_from_queue(self, chat_id: int, index: int) -> Optional[QueueItem]:
        """Remove item from queue by index"""
        if chat_id not in self._queues:
            return None
        
        queue = self._queues[chat_id]
        removed = queue.remove(index)
        
        if removed:
            self._metrics['queue_operations'] += 1
            logger.debug(f"Removed '{removed.title}' from queue at index {index} for chat {chat_id}")
        
        return removed
    
    def clear_queue(self, chat_id: int) -> int:
        """Clear queue and return number of items removed"""
        if chat_id not in self._queues:
            return 0
        
        queue = self._queues[chat_id]
        count = len(queue)
        queue.clear()
        
        self._metrics['queue_operations'] += 1
        logger.debug(f"Cleared {count} items from queue for chat {chat_id}")
        
        return count
    
    def get_next_song(self, chat_id: int) -> Optional[QueueItem]:
        """Get next song from queue"""
        if chat_id not in self._queues:
            return None
        
        queue = self._queues[chat_id]
        return queue.pop()
    
    def get_queue_items(self, chat_id: int, limit: Optional[int] = None) -> List[QueueItem]:
        """Get queue items for display"""
        if chat_id not in self._queues:
            return []
        
        queue = self._queues[chat_id]
        return queue.get_items(limit)
    
    def set_current_track(self, chat_id: int, track: Optional[QueueItem]):
        """Set current playing track"""
        self._current_tracks[chat_id] = track
        self._last_activity[chat_id] = time.time()
    
    def get_current_track(self, chat_id: int) -> Optional[QueueItem]:
        """Get current playing track"""
        return self._current_tracks.get(chat_id)
    
    def get_queue_status(self, chat_id: int) -> Dict[str, Any]:
        """Get comprehensive queue status"""
        current_track = self.get_current_track(chat_id)
        queue_items = self.get_queue_items(chat_id, limit=10)
        
        return {
            'chat_id': chat_id,
            'current_track': current_track.to_dict() if current_track else None,
            'queue_length': len(self._queues.get(chat_id, [])),
            'queue_items': [item.to_dict() for item in queue_items],
            'last_activity': self._last_activity.get(chat_id, 0),
            'has_worker': chat_id in self._queue_workers and not self._queue_workers[chat_id].done()
        }
    
    def cleanup_inactive_chats(self, max_idle_time: int = 3600) -> List[int]:
        """Cleanup inactive chats and return list of cleaned chat IDs"""
        current_time = time.time()
        inactive_chats = []
        
        for chat_id, last_activity in self._last_activity.items():
            if current_time - last_activity > max_idle_time:
                # Check if queue is empty and nothing is playing
                if (chat_id not in self._queues or not self._queues[chat_id]) and \
                   (chat_id not in self._current_tracks or not self._current_tracks[chat_id]):
                    inactive_chats.append(chat_id)
        
        # Cleanup inactive chats
        for chat_id in inactive_chats:
            self._queues.pop(chat_id, None)
            self._current_tracks.pop(chat_id, None)
            self._last_activity.pop(chat_id, None)
            
            # Stop queue worker if running
            if chat_id in self._queue_workers:
                worker = self._queue_workers.pop(chat_id)
                if not worker.done():
                    worker.cancel()
        
        if inactive_chats:
            logger.info(f"Cleaned up {len(inactive_chats)} inactive chats: {inactive_chats}")
        
        return inactive_chats
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            **self._metrics,
            'active_chats': len(self._queues),
            'total_queue_items': sum(len(q) for q in self._queues.values()),
            'active_workers': sum(1 for w in self._queue_workers.values() if not w.done())
        }
    
    def reset_metrics(self):
        """Reset performance metrics"""
        for key in self._metrics:
            self._metrics[key] = 0

# Global queue manager instance
queue_manager = QueueManager()

# Compatibility functions for existing code
async def get_queue(chat_id: int) -> List[Dict[str, Any]]:
    """Compatibility function - get queue as list of dicts"""
    items = queue_manager.get_queue_items(chat_id)
    return [item.to_dict() for item in items]

async def add_to_queue(chat_id: int, song_data: Dict[str, Any]) -> int:
    """Compatibility function - add to queue"""
    position, _ = queue_manager.add_to_queue(chat_id, song_data)
    return position

async def clear_queue(chat_id: int) -> Tuple[bool, str]:
    """Compatibility function - clear queue"""
    count = queue_manager.clear_queue(chat_id)
    if count > 0:
        return True, f"✅ Cleared {count} songs from queue"
    return True, "✅ Queue was already empty"

async def remove_from_queue(chat_id: int, position: int) -> Tuple[bool, str]:
    """Compatibility function - remove from queue"""
    # Convert to 0-based index
    index = position - 1
    removed = queue_manager.remove_from_queue(chat_id, index)
    
    if removed:
        return True, f"✅ Removed '{removed.title}' from queue"
    return False, "❌ Invalid queue position"
