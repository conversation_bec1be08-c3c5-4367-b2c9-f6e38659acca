"""
YouTube Service - Enhanced YouTube downloader with optimization
"""

import asyncio
import os
import yt_dlp
from typing import Dict, List, Optional, Callable
from loguru import logger
import aiofiles
import hashlib
from datetime import datetime
import json

class YouTubeService:
    """Enhanced YouTube service with caching and optimization"""
    
    def __init__(self, config):
        """Initialize YouTube service"""
        self.config = config
        self.download_dir = os.path.join(config.base_dir, config.download_dir)
        self.cache_dir = os.path.join(config.base_dir, config.cache_dir)
        
        # Ensure directories exist
        os.makedirs(self.download_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # YT-DLP options
        self.ydl_opts = {
            'format': 'bestaudio/best',
            'quiet': True,
            'no_warnings': True,
            'extract_flat': False,
            'nocheckcertificate': True,
            'ignoreerrors': False,
            'logtostderr': False,
            'no_color': True,
            'noprogress': False,
            'ffmpeg_location': os.path.join(config.base_dir, 'ffmpeg', 'bin'),
            'cachedir': self.cache_dir,
            'cookiefile': os.path.join(self.cache_dir, 'cookies.txt') if os.path.exists(os.path.join(self.cache_dir, 'cookies.txt')) else None,
        }
        
        # Cache for video info
        self.info_cache = {}
        
    async def search(self, query: str, limit: int = 10) -> List[Dict]:
        """Search YouTube for videos"""
        try:
            opts = self.ydl_opts.copy()
            opts.update({
                'extract_flat': 'in_playlist',
                'quiet': True,
                'no_warnings': True,
                'default_search': 'ytsearch',
                'playlist_items': f'1-{limit}'
            })
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                result = await asyncio.to_thread(
                    ydl.extract_info,
                    f"ytsearch{limit}:{query}",
                    download=False
                )
            
            videos = []
            for entry in result.get('entries', []):
                videos.append({
                    'title': entry.get('title', 'Unknown'),
                    'url': f"https://youtube.com/watch?v={entry['id']}",
                    'duration': entry.get('duration', 0),
                    'thumbnail': self._get_best_thumbnail(entry.get('thumbnails', [])),
                    'channel': entry.get('channel', 'Unknown'),
                    'views': entry.get('view_count', 0),
                    'upload_date': entry.get('upload_date'),
                    'youtube_id': entry['id']
                })
            
            return videos
            
        except Exception as e:
            logger.error(f"Error searching YouTube: {e}")
            return []
    
    async def get_video_info(self, url: str) -> Optional[Dict]:
        """Get video information without downloading"""
        try:
            # Check cache first
            url_hash = hashlib.md5(url.encode()).hexdigest()
            if url_hash in self.info_cache:
                cached = self.info_cache[url_hash]
                # Cache for 1 hour
                if (datetime.now() - cached['timestamp']).seconds < 3600:
                    return cached['info']
            
            opts = self.ydl_opts.copy()
            opts['quiet'] = True
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                info = await asyncio.to_thread(
                    ydl.extract_info,
                    url,
                    download=False
                )
            
            video_info = {
                'title': info.get('title', 'Unknown'),
                'url': info.get('webpage_url', url),
                'duration': info.get('duration', 0),
                'thumbnail': self._get_best_thumbnail(info.get('thumbnails', [])),
                'channel': info.get('channel', 'Unknown'),
                'views': info.get('view_count', 0),
                'upload_date': info.get('upload_date'),
                'description': info.get('description', ''),
                'youtube_id': info.get('id'),
                'audio_url': self._get_audio_url(info),
                'filesize': info.get('filesize') or info.get('filesize_approx', 0)
            }
            
            # Cache the info
            self.info_cache[url_hash] = {
                'info': video_info,
                'timestamp': datetime.now()
            }
            
            return video_info
            
        except Exception as e:
            logger.error(f"Error getting video info: {e}")
            return None
    
    async def download_audio(
        self, 
        url: str, 
        quality: str = 'high',
        progress_callback: Optional[Callable] = None
    ) -> Optional[str]:
        """Download audio from YouTube with progress callback"""
        try:
            # Get quality preset
            from config_enhanced import AUDIO_QUALITY_PRESETS
            quality_preset = AUDIO_QUALITY_PRESETS.get(quality, AUDIO_QUALITY_PRESETS['high'])
            
            # Check if already downloaded
            info = await self.get_video_info(url)
            if not info:
                return None
            
            # Generate filename
            safe_title = "".join(c for c in info['title'] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_title}_{quality}.mp3"
            filepath = os.path.join(self.download_dir, filename)
            
            # Check if file exists and is valid
            if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                logger.info(f"Using cached file: {filename}")
                if progress_callback:
                    await progress_callback(100)
                return filepath
            
            # Download options
            opts = self.ydl_opts.copy()
            opts.update({
                'format': quality_preset['format'],
                'outtmpl': os.path.join(self.download_dir, '%(title)s.%(ext)s'),
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': str(quality_preset['bitrate']),
                }],
                'postprocessor_args': [
                    '-ar', str(quality_preset['sample_rate']),
                ],
                'keepvideo': False,
            })
            
            # Progress hook
            if progress_callback:
                async def progress_hook(d):
                    if d['status'] == 'downloading':
                        try:
                            total = d.get('total_bytes') or d.get('total_bytes_estimate', 0)
                            downloaded = d.get('downloaded_bytes', 0)
                            if total > 0:
                                percent = int((downloaded / total) * 100)
                                await progress_callback(percent)
                        except:
                            pass
                
                opts['progress_hooks'] = [lambda d: asyncio.create_task(progress_hook(d))]
            
            # Download
            with yt_dlp.YoutubeDL(opts) as ydl:
                await asyncio.to_thread(ydl.download, [url])
            
            # Find the downloaded file
            for file in os.listdir(self.download_dir):
                if file.endswith('.mp3') and safe_title in file:
                    final_path = os.path.join(self.download_dir, file)
                    if os.path.exists(final_path):
                        # Rename to our standard format
                        if final_path != filepath:
                            try:
                                os.rename(final_path, filepath)
                            except:
                                filepath = final_path
                        
                        logger.info(f"Downloaded: {filename}")
                        if progress_callback:
                            await progress_callback(100)
                        return filepath
            
            logger.error("Downloaded file not found")
            return None
            
        except Exception as e:
            logger.error(f"Error downloading audio: {e}")
            return None
    
    async def get_playlist_videos(self, url: str) -> Optional[List[Dict]]:
        """Get all videos from a YouTube playlist"""
        try:
            opts = self.ydl_opts.copy()
            opts.update({
                'extract_flat': True,
                'quiet': True,
            })
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                result = await asyncio.to_thread(
                    ydl.extract_info,
                    url,
                    download=False
                )
            
            videos = []
            for entry in result.get('entries', []):
                videos.append({
                    'title': entry.get('title', 'Unknown'),
                    'url': f"https://youtube.com/watch?v={entry['id']}",
                    'duration': entry.get('duration', 0),
                    'youtube_id': entry['id']
                })
            
            return videos
            
        except Exception as e:
            logger.error(f"Error getting playlist: {e}")
            return None
    
    async def get_channel_videos(self, url: str, limit: int = 20) -> Optional[List[Dict]]:
        """Get recent videos from a YouTube channel"""
        try:
            opts = self.ydl_opts.copy()
            opts.update({
                'extract_flat': True,
                'quiet': True,
                'playlist_items': f'1-{limit}'
            })
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                result = await asyncio.to_thread(
                    ydl.extract_info,
                    url,
                    download=False
                )
            
            videos = []
            for entry in result.get('entries', [])[:limit]:
                videos.append({
                    'title': entry.get('title', 'Unknown'),
                    'url': f"https://youtube.com/watch?v={entry['id']}",
                    'duration': entry.get('duration', 0),
                    'upload_date': entry.get('upload_date'),
                    'youtube_id': entry['id']
                })
            
            return videos
            
        except Exception as e:
            logger.error(f"Error getting channel videos: {e}")
            return None
    
    async def get_trending_music(self, region: str = 'US', limit: int = 20) -> List[Dict]:
        """Get trending music videos"""
        try:
            # YouTube Music trending URL
            url = f"https://www.youtube.com/feed/music"
            
            opts = self.ydl_opts.copy()
            opts.update({
                'extract_flat': True,
                'quiet': True,
                'geo_bypass_country': region,
                'playlist_items': f'1-{limit}'
            })
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                # Search for trending music
                result = await asyncio.to_thread(
                    ydl.extract_info,
                    f"ytsearch{limit}:trending music {region}",
                    download=False
                )
            
            videos = []
            for entry in result.get('entries', []):
                videos.append({
                    'title': entry.get('title', 'Unknown'),
                    'url': f"https://youtube.com/watch?v={entry['id']}",
                    'duration': entry.get('duration', 0),
                    'thumbnail': self._get_best_thumbnail(entry.get('thumbnails', [])),
                    'channel': entry.get('channel', 'Unknown'),
                    'views': entry.get('view_count', 0),
                    'youtube_id': entry['id']
                })
            
            return videos
            
        except Exception as e:
            logger.error(f"Error getting trending music: {e}")
            return []
    
    async def cleanup_downloads(self, max_age_days: int = 7):
        """Clean up old downloaded files"""
        try:
            now = datetime.now()
            cleaned = 0
            
            for file in os.listdir(self.download_dir):
                filepath = os.path.join(self.download_dir, file)
                if os.path.isfile(filepath):
                    file_age = now - datetime.fromtimestamp(os.path.getmtime(filepath))
                    if file_age.days > max_age_days:
                        os.remove(filepath)
                        cleaned += 1
            
            logger.info(f"Cleaned up {cleaned} old download files")
            return cleaned
            
        except Exception as e:
            logger.error(f"Error cleaning up downloads: {e}")
            return 0
    
    def _get_best_thumbnail(self, thumbnails: List[Dict]) -> Optional[str]:
        """Get the best quality thumbnail from list"""
        if not thumbnails:
            return None
        
        # Sort by resolution
        sorted_thumbs = sorted(
            thumbnails,
            key=lambda x: x.get('width', 0) * x.get('height', 0),
            reverse=True
        )
        
        # Return the highest quality
        return sorted_thumbs[0].get('url') if sorted_thumbs else None
    
    def _get_audio_url(self, info: Dict) -> Optional[str]:
        """Extract direct audio URL from video info"""
        try:
            # Look for audio formats
            formats = info.get('formats', [])
            audio_formats = [f for f in formats if f.get('acodec') != 'none' and f.get('vcodec') == 'none']
            
            if audio_formats:
                # Sort by quality
                audio_formats.sort(key=lambda x: x.get('abr', 0), reverse=True)
                return audio_formats[0].get('url')
            
            # Fallback to best format
            if formats:
                return formats[0].get('url')
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting audio URL: {e}")
            return None
