"""
Enhanced Telegram Music Bot using Hydrogram
Optimized for performance with advanced features
"""

import asyncio
import os
import sys
from typing import Dict, List, Optional, Union
from datetime import datetime, timedelta
import logging
from functools import wraps
import time

# Configure logging
from loguru import logger
from hydrogram import Client, filters, idle
from hydrogram.types import (
    Message, InlineKeyboardMarkup, InlineKeyboardButton, 
    CallbackQuery, User, Chat
)
from hydrogram.errors import FloodWait, UserNotParticipant
from hydrogram.enums import ChatType, ParseMode

# Import enhanced configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config_enhanced import config, TRANSLATIONS, AUDIO_QUALITY_PRESETS, EQUALIZER_PRESETS

# Import enhanced modules
from ..services.spotify_service import SpotifyService
from ..services.soundcloud_service import SoundCloudService
from ..services.youtube_service import YouTubeService
from ..services.lyrics_service import LyricsService
from ..services.radio_service import RadioService
from ..utils.cache_manager import CacheManager
from ..utils.database import DatabaseManager
from ..utils.rate_limiter import RateLimiter
from ..utils.analytics import AnalyticsManager
from ..player.voice_chat_enhanced import VoiceManager
from ..utils.language import LanguageManager

# Initialize logger
logger.remove()
logger.add(
    os.path.join(config.logs_dir, "bot_{time}.log"),
    rotation="1 day",
    retention="7 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
)
logger.add(sys.stderr, level="INFO", colorize=True)

class EnhancedMusicBot:
    """Enhanced Music Bot with advanced features and optimizations"""
    
    def __init__(self):
        """Initialize the enhanced music bot"""
        self.config = config
        self.bot_start_time = datetime.now()
        
        # Initialize Hydrogram clients
        self.bot = Client(
            f"enhanced_music_bot_{int(time.time())}",
            api_id=config.api_id,
            api_hash=config.api_hash,
            bot_token=config.bot_token
        )
        
        self.user = Client(
            f"user_session_{int(time.time())}",
            api_id=config.api_id,
            api_hash=config.api_hash,
            session_string=config.session_string
        )
        
        # Initialize services
        self.spotify = SpotifyService(config.spotify_client_id, config.spotify_client_secret) if config.enable_spotify else None
        self.soundcloud = SoundCloudService(config.soundcloud_client_id) if config.enable_soundcloud else None
        self.youtube = YouTubeService(config)
        self.lyrics = LyricsService(config.genius_api_key) if config.show_lyrics else None
        self.radio = RadioService() if config.enable_radio else None
        
        # Initialize utilities
        self.cache = CacheManager(config)
        self.db = DatabaseManager(config)
        self.rate_limiter = RateLimiter(config)
        self.analytics = AnalyticsManager(config) if config.enable_analytics else None
        self.voice_manager = VoiceManager(self.user, config)
        self.lang_manager = LanguageManager(config)
        
        # User preferences and states
        self.user_preferences: Dict[int, Dict] = {}
        self.user_states: Dict[int, str] = {}
        self.active_downloads: Dict[str, Dict] = {}
        
        # Register handlers
        self._register_handlers()
        
    def _register_handlers(self):
        """Register all command and callback handlers"""
        
        # Basic commands
        self.bot.on_message(filters.command("start") & filters.private)(self.cmd_start)
        self.bot.on_message(filters.command("help"))(self.cmd_help)
        self.bot.on_message(filters.command("settings") & filters.private)(self.cmd_settings)
        self.bot.on_message(filters.command("language"))(self.cmd_language)
        
        # Music commands
        self.bot.on_message(filters.command("play"))(self.cmd_play)
        self.bot.on_message(filters.command("spotify") & self._spotify_enabled)(self.cmd_spotify)
        self.bot.on_message(filters.command("soundcloud") & self._soundcloud_enabled)(self.cmd_soundcloud)
        self.bot.on_message(filters.command("search"))(self.cmd_search)
        self.bot.on_message(filters.command("lyrics") & self._lyrics_enabled)(self.cmd_lyrics)
        
        # Voice chat controls
        self.bot.on_message(filters.command("join"))(self.cmd_join)
        self.bot.on_message(filters.command("leave"))(self.cmd_leave)
        self.bot.on_message(filters.command("pause"))(self.cmd_pause)
        self.bot.on_message(filters.command("resume"))(self.cmd_resume)
        self.bot.on_message(filters.command("skip"))(self.cmd_skip)
        self.bot.on_message(filters.command("stop"))(self.cmd_stop)
        self.bot.on_message(filters.command("volume"))(self.cmd_volume)
        self.bot.on_message(filters.command("seek"))(self.cmd_seek)
        self.bot.on_message(filters.command("equalizer") & self._equalizer_enabled)(self.cmd_equalizer)
        
        # Queue management
        self.bot.on_message(filters.command("queue"))(self.cmd_queue)
        self.bot.on_message(filters.command("shuffle"))(self.cmd_shuffle)
        self.bot.on_message(filters.command("loop"))(self.cmd_loop)
        self.bot.on_message(filters.command("clear"))(self.cmd_clear)
        self.bot.on_message(filters.command("remove"))(self.cmd_remove)
        self.bot.on_message(filters.command("move"))(self.cmd_move)
        
        # Playlist management
        self.bot.on_message(filters.command("playlist"))(self.cmd_playlist)
        self.bot.on_message(filters.command("createplaylist"))(self.cmd_create_playlist)
        self.bot.on_message(filters.command("deleteplaylist"))(self.cmd_delete_playlist)
        self.bot.on_message(filters.command("addtoplaylist"))(self.cmd_add_to_playlist)
        self.bot.on_message(filters.command("removefromplaylist"))(self.cmd_remove_from_playlist)
        self.bot.on_message(filters.command("playlists"))(self.cmd_playlists)
        self.bot.on_message(filters.command("shareplaylist"))(self.cmd_share_playlist)
        
        # Radio commands
        self.bot.on_message(filters.command("radio") & self._radio_enabled)(self.cmd_radio)
        self.bot.on_message(filters.command("stations"))(self.cmd_stations)
        
        # Statistics and info
        self.bot.on_message(filters.command("stats"))(self.cmd_stats)
        self.bot.on_message(filters.command("nowplaying"))(self.cmd_now_playing)
        self.bot.on_message(filters.command("history"))(self.cmd_history)
        
        # Admin commands
        self.bot.on_message(filters.command("broadcast") & self._is_admin)(self.cmd_broadcast)
        self.bot.on_message(filters.command("maintenance") & self._is_admin)(self.cmd_maintenance)
        self.bot.on_message(filters.command("cache") & self._is_admin)(self.cmd_cache)
        self.bot.on_message(filters.command("users") & self._is_admin)(self.cmd_users)
        
        # Callback query handler
        self.bot.on_callback_query()(self.handle_callback_query)
        
        # Inline query handler for search
        self.bot.on_inline_query()(self.handle_inline_query)
        
    # Filters
    def _spotify_enabled(self, _, __, ___):
        return config.enable_spotify
    
    def _soundcloud_enabled(self, _, __, ___):
        return config.enable_soundcloud
    
    def _lyrics_enabled(self, _, __, ___):
        return config.show_lyrics
    
    def _equalizer_enabled(self, _, __, ___):
        return config.enable_equalizer
    
    def _radio_enabled(self, _, __, ___):
        return config.enable_radio
    
    def _is_admin(self, _, __, message: Message):
        return message.from_user.id in config.admin_ids
    
    # Rate limiting decorator
    def rate_limit(limit_type="commands"):
        def decorator(func):
            @wraps(func)
            async def wrapper(self, client, message: Message):
                user_id = message.from_user.id
                if not await self.rate_limiter.check_limit(user_id, limit_type):
                    await message.reply_text(
                        self.lang_manager.get_text(user_id, "rate_limit_exceeded"),
                        parse_mode=ParseMode.MARKDOWN
                    )
                    return
                return await func(self, client, message)
            return wrapper
        return decorator
    
    # Command handlers
    @rate_limit()
    async def cmd_start(self, client: Client, message: Message):
        """Handle /start command"""
        user_id = message.from_user.id
        user_name = message.from_user.first_name
        
        # Save user to database
        await self.db.save_user(user_id, user_name)
        
        # Get user language
        lang = await self.lang_manager.get_user_language(user_id)
        
        # Create welcome message with inline keyboard
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat=""),
                InlineKeyboardButton("📋 Commands", callback_data="help")
            ],
            [
                InlineKeyboardButton("⚙️ Settings", callback_data="settings"),
                InlineKeyboardButton("📊 Stats", callback_data="stats")
            ],
            [
                InlineKeyboardButton("🌐 Language", callback_data="language"),
                InlineKeyboardButton("❓ Support", url="https://t.me/support")
            ]
        ])
        
        welcome_text = f"""
🎵 **{TRANSLATIONS[lang]['welcome']}**

Hello {user_name}! I'm an enhanced music bot with advanced features:

✨ **Features:**
• High-quality music streaming
• Spotify & SoundCloud support
• Lyrics display
• Audio equalizer
• Volume control & seeking
• Radio stations
• Collaborative playlists
• Multi-language support
• And much more!

Use the buttons below or send /help to get started.
        """
        
        await message.reply_text(
            welcome_text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Track analytics
        if self.analytics:
            await self.analytics.track_event(user_id, "bot_start")
    
    @rate_limit()
    async def cmd_play(self, client: Client, message: Message):
        """Handle /play command - Universal play command"""
        user_id = message.from_user.id
        chat_id = message.chat.id
        
        # Extract query
        query = message.text.split(maxsplit=1)
        if len(query) < 2:
            await message.reply_text(
                "❌ Please provide a song name or URL.\nUsage: `/play <song name or URL>`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        query = query[1]
        
        # Send searching message
        status_msg = await message.reply_text("🔍 Searching...")
        
        try:
            # Check if it's a URL or search query
            if any(domain in query for domain in ["youtube.com", "youtu.be", "spotify.com", "soundcloud.com"]):
                # Direct URL
                if "spotify.com" in query and self.spotify:
                    result = await self.spotify.get_track_info(query)
                elif "soundcloud.com" in query and self.soundcloud:
                    result = await self.soundcloud.get_track_info(query)
                else:
                    result = await self.youtube.get_video_info(query)
            else:
                # Search query - try YouTube first
                results = await self.youtube.search(query, limit=1)
                if results:
                    result = results[0]
                else:
                    await status_msg.edit_text("❌ No results found.")
                    return
            
            # Update status
            await status_msg.edit_text(f"⬇️ Downloading: **{result['title']}**", parse_mode=ParseMode.MARKDOWN)
            
            # Download with progress callback
            download_id = f"{chat_id}_{int(time.time())}"
            self.active_downloads[download_id] = {
                "title": result['title'],
                "progress": 0,
                "status": "downloading"
            }
            
            async def progress_callback(progress):
                self.active_downloads[download_id]["progress"] = progress
                if progress % 20 == 0:  # Update every 20%
                    await status_msg.edit_text(
                        f"⬇️ Downloading: **{result['title']}**\nProgress: {progress}%",
                        parse_mode=ParseMode.MARKDOWN
                    )
            
            # Download audio
            audio_file = await self.youtube.download_audio(
                result['url'],
                quality=await self.get_user_audio_quality(user_id),
                progress_callback=progress_callback
            )
            
            # Join voice chat if not already
            if not await self.voice_manager.is_connected(chat_id):
                await self.voice_manager.join_chat(chat_id)
            
            # Add to queue or play
            queue_position = await self.voice_manager.add_to_queue(
                chat_id,
                {
                    "file": audio_file,
                    "title": result['title'],
                    "duration": result.get('duration', 0),
                    "thumbnail": result.get('thumbnail'),
                    "requested_by": user_id
                }
            )
            
            # Create now playing card
            if queue_position == 0:
                await status_msg.delete()
                await self.send_now_playing(message, result)
            else:
                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("📋 View Queue", callback_data="queue"),
                        InlineKeyboardButton("⏭️ Skip", callback_data="skip")
                    ]
                ])
                await status_msg.edit_text(
                    f"✅ **{result['title']}** added to queue.\nPosition: #{queue_position + 1}",
                    reply_markup=keyboard,
                    parse_mode=ParseMode.MARKDOWN
                )
            
            # Clean up download tracking
            del self.active_downloads[download_id]
            
            # Track analytics
            if self.analytics:
                await self.analytics.track_play(user_id, result['title'], "youtube")
                
        except Exception as e:
            logger.error(f"Error in play command: {e}")
            await status_msg.edit_text("❌ An error occurred while processing your request.")
    
    async def send_now_playing(self, message: Message, track_info: dict):
        """Send now playing message with rich media card"""
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⏸️ Pause", callback_data="pause"),
                InlineKeyboardButton("⏭️ Skip", callback_data="skip"),
                InlineKeyboardButton("⏹️ Stop", callback_data="stop")
            ],
            [
                InlineKeyboardButton("🔊 Volume", callback_data="volume"),
                InlineKeyboardButton("🎚️ Equalizer", callback_data="equalizer"),
                InlineKeyboardButton("🎵 Lyrics", callback_data="lyrics")
            ],
            [
                InlineKeyboardButton("📋 Queue", callback_data="queue"),
                InlineKeyboardButton("🔀 Shuffle", callback_data="shuffle"),
                InlineKeyboardButton("🔁 Loop", callback_data="loop")
            ]
        ])
        
        caption = f"""
🎵 **Now Playing**

**Title:** {track_info['title']}
**Duration:** {self.format_duration(track_info.get('duration', 0))}
**Requested by:** {message.from_user.mention}

Use the controls below to manage playback.
        """
        
        if track_info.get('thumbnail') and config.show_thumbnails:
            try:
                await message.reply_photo(
                    photo=track_info['thumbnail'],
                    caption=caption,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.MARKDOWN
                )
            except:
                await message.reply_text(
                    caption,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.MARKDOWN
                )
        else:
            await message.reply_text(
                caption,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def get_user_audio_quality(self, user_id: int) -> str:
        """Get user's preferred audio quality"""
        prefs = await self.db.get_user_preferences(user_id)
        return prefs.get("audio_quality", config.default_audio_quality)
    
    @staticmethod
    def format_duration(seconds: int) -> str:
        """Format duration in seconds to human readable format"""
        if seconds == 0:
            return "Live"
        
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    async def run(self):
        """Start the bot"""
        logger.info("Starting Enhanced Music Bot...")
        
        # Initialize database
        await self.db.initialize()
        
        # Start clients
        await self.bot.start()
        await self.user.start()
        
        # Initialize voice manager
        await self.voice_manager.initialize()
        
        # Start background tasks
        asyncio.create_task(self.auto_leave_task())
        asyncio.create_task(self.cache_cleanup_task())
        
        if self.analytics:
            asyncio.create_task(self.analytics.start_metrics_server())
        
        logger.info("Enhanced Music Bot started successfully!")
        
        # Keep the bot running
        await idle()
        
        # Cleanup on shutdown
        await self.shutdown()
    
    async def auto_leave_task(self):
        """Background task to auto-leave inactive voice chats"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                inactive_chats = await self.voice_manager.get_inactive_chats(
                    config.auto_leave_minutes
                )
                
                for chat_id in inactive_chats:
                    await self.voice_manager.leave_chat(chat_id)
                    logger.info(f"Auto-left inactive chat: {chat_id}")
                    
            except Exception as e:
                logger.error(f"Error in auto-leave task: {e}")
    
    async def cache_cleanup_task(self):
        """Background task to clean up old cache"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                cleaned = await self.cache.cleanup_old_files()
                if cleaned > 0:
                    logger.info(f"Cleaned up {cleaned} old cache files")
                    
            except Exception as e:
                logger.error(f"Error in cache cleanup task: {e}")
    
    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("Shutting down Enhanced Music Bot...")
        
        # Leave all voice chats
        await self.voice_manager.leave_all_chats()
        
        # Close database connections
        await self.db.close()
        
        # Stop clients
        await self.bot.stop()
        await self.user.stop()
        
        logger.info("Enhanced Music Bot shut down successfully!")

# Create bot instance
bot = EnhancedMusicBot()

if __name__ == "__main__":
    # Run the bot
    asyncio.run(bot.run())
