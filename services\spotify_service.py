"""
Spotify Service - Integration with Spotify API
"""

import asyncio
import spotipy
from spotipy.oauth2 import SpotifyClientCredentials
from typing import Dict, List, Optional
import re
from loguru import logger

class SpotifyService:
    """Service for interacting with Spotify API"""
    
    def __init__(self, client_id: str, client_secret: str):
        """Initialize Spotify service"""
        self.enabled = bool(client_id and client_secret)
        if self.enabled:
            try:
                auth_manager = SpotifyClientCredentials(
                    client_id=client_id,
                    client_secret=client_secret
                )
                self.sp = spotipy.Spotify(auth_manager=auth_manager)
                logger.info("Spotify service initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Spotify service: {e}")
                self.enabled = False
        else:
            logger.warning("Spotify service disabled - missing credentials")
    
    async def get_track_info(self, url: str) -> Optional[Dict]:
        """Get track information from Spotify URL"""
        if not self.enabled:
            return None
        
        try:
            # Extract track ID from URL
            track_id = self._extract_track_id(url)
            if not track_id:
                return None
            
            # Get track info from Spotify API
            track = await asyncio.to_thread(self.sp.track, track_id)
            
            # Format response
            return {
                "title": f"{track['artists'][0]['name']} - {track['name']}",
                "artist": track['artists'][0]['name'],
                "album": track['album']['name'],
                "duration": track['duration_ms'] // 1000,
                "thumbnail": track['album']['images'][0]['url'] if track['album']['images'] else None,
                "url": track['external_urls']['spotify'],
                "spotify_id": track_id,
                "isrc": track.get('external_ids', {}).get('isrc'),
                "preview_url": track.get('preview_url'),
                "popularity": track.get('popularity', 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting Spotify track info: {e}")
            return None
    
    async def get_playlist_tracks(self, url: str) -> Optional[List[Dict]]:
        """Get all tracks from a Spotify playlist"""
        if not self.enabled:
            return None
        
        try:
            # Extract playlist ID from URL
            playlist_id = self._extract_playlist_id(url)
            if not playlist_id:
                return None
            
            # Get playlist tracks
            results = await asyncio.to_thread(
                self.sp.playlist_tracks,
                playlist_id,
                fields='items(track(name,artists,album,duration_ms,external_urls,id))'
            )
            
            tracks = []
            for item in results['items']:
                if item['track']:
                    track = item['track']
                    tracks.append({
                        "title": f"{track['artists'][0]['name']} - {track['name']}",
                        "artist": track['artists'][0]['name'],
                        "album": track['album']['name'],
                        "duration": track['duration_ms'] // 1000,
                        "url": track['external_urls']['spotify'],
                        "spotify_id": track['id']
                    })
            
            # Handle pagination
            while results['next']:
                results = await asyncio.to_thread(self.sp.next, results)
                for item in results['items']:
                    if item['track']:
                        track = item['track']
                        tracks.append({
                            "title": f"{track['artists'][0]['name']} - {track['name']}",
                            "artist": track['artists'][0]['name'],
                            "album": track['album']['name'],
                            "duration": track['duration_ms'] // 1000,
                            "url": track['external_urls']['spotify'],
                            "spotify_id": track['id']
                        })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error getting Spotify playlist: {e}")
            return None
    
    async def search(self, query: str, limit: int = 10) -> Optional[List[Dict]]:
        """Search for tracks on Spotify"""
        if not self.enabled:
            return None
        
        try:
            # Search Spotify
            results = await asyncio.to_thread(
                self.sp.search,
                q=query,
                type='track',
                limit=limit
            )
            
            tracks = []
            for track in results['tracks']['items']:
                tracks.append({
                    "title": f"{track['artists'][0]['name']} - {track['name']}",
                    "artist": track['artists'][0]['name'],
                    "album": track['album']['name'],
                    "duration": track['duration_ms'] // 1000,
                    "thumbnail": track['album']['images'][0]['url'] if track['album']['images'] else None,
                    "url": track['external_urls']['spotify'],
                    "spotify_id": track['id'],
                    "popularity": track.get('popularity', 0)
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error searching Spotify: {e}")
            return None
    
    async def get_artist_top_tracks(self, artist_url: str, country: str = 'US') -> Optional[List[Dict]]:
        """Get top tracks for an artist"""
        if not self.enabled:
            return None
        
        try:
            # Extract artist ID
            artist_id = self._extract_artist_id(artist_url)
            if not artist_id:
                return None
            
            # Get top tracks
            results = await asyncio.to_thread(
                self.sp.artist_top_tracks,
                artist_id,
                country=country
            )
            
            tracks = []
            for track in results['tracks']:
                tracks.append({
                    "title": f"{track['artists'][0]['name']} - {track['name']}",
                    "artist": track['artists'][0]['name'],
                    "album": track['album']['name'],
                    "duration": track['duration_ms'] // 1000,
                    "thumbnail": track['album']['images'][0]['url'] if track['album']['images'] else None,
                    "url": track['external_urls']['spotify'],
                    "spotify_id": track['id'],
                    "popularity": track.get('popularity', 0)
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error getting artist top tracks: {e}")
            return None
    
    async def get_album_tracks(self, album_url: str) -> Optional[List[Dict]]:
        """Get all tracks from an album"""
        if not self.enabled:
            return None
        
        try:
            # Extract album ID
            album_id = self._extract_album_id(album_url)
            if not album_id:
                return None
            
            # Get album info
            album = await asyncio.to_thread(self.sp.album, album_id)
            
            tracks = []
            for track in album['tracks']['items']:
                tracks.append({
                    "title": f"{track['artists'][0]['name']} - {track['name']}",
                    "artist": track['artists'][0]['name'],
                    "album": album['name'],
                    "duration": track['duration_ms'] // 1000,
                    "thumbnail": album['images'][0]['url'] if album['images'] else None,
                    "url": track['external_urls']['spotify'],
                    "spotify_id": track['id'],
                    "track_number": track['track_number']
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error getting album tracks: {e}")
            return None
    
    async def get_recommendations(self, track_ids: List[str], limit: int = 20) -> Optional[List[Dict]]:
        """Get recommendations based on tracks"""
        if not self.enabled:
            return None
        
        try:
            # Get recommendations
            results = await asyncio.to_thread(
                self.sp.recommendations,
                seed_tracks=track_ids[:5],  # Max 5 seeds
                limit=limit
            )
            
            tracks = []
            for track in results['tracks']:
                tracks.append({
                    "title": f"{track['artists'][0]['name']} - {track['name']}",
                    "artist": track['artists'][0]['name'],
                    "album": track['album']['name'],
                    "duration": track['duration_ms'] // 1000,
                    "thumbnail": track['album']['images'][0]['url'] if track['album']['images'] else None,
                    "url": track['external_urls']['spotify'],
                    "spotify_id": track['id'],
                    "popularity": track.get('popularity', 0)
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error getting recommendations: {e}")
            return None
    
    def _extract_track_id(self, url: str) -> Optional[str]:
        """Extract track ID from Spotify URL"""
        patterns = [
            r'spotify\.com/track/([a-zA-Z0-9]+)',
            r'spotify\.com/intl-[a-z]+/track/([a-zA-Z0-9]+)',
            r'spotify\.link/([a-zA-Z0-9]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_playlist_id(self, url: str) -> Optional[str]:
        """Extract playlist ID from Spotify URL"""
        patterns = [
            r'spotify\.com/playlist/([a-zA-Z0-9]+)',
            r'spotify\.com/user/[^/]+/playlist/([a-zA-Z0-9]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_artist_id(self, url: str) -> Optional[str]:
        """Extract artist ID from Spotify URL"""
        pattern = r'spotify\.com/artist/([a-zA-Z0-9]+)'
        match = re.search(pattern, url)
        return match.group(1) if match else None
    
    def _extract_album_id(self, url: str) -> Optional[str]:
        """Extract album ID from Spotify URL"""
        pattern = r'spotify\.com/album/([a-zA-Z0-9]+)'
        match = re.search(pattern, url)
        return match.group(1) if match else None
