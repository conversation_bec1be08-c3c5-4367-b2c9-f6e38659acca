"""
Enhanced Configuration System for Telegram Music Bot
Supports environment variables and advanced features
"""
import os
from typing import Dict, List, Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class BotConfig(BaseSettings):
    """Bot configuration with validation and defaults"""
    
    # Telegram API Configuration
    api_id: int = Field(default=24702931, env="API_ID")
    api_hash: str = Field(default="358b6bcdd97c5cf94c58d44b203e4f8f", env="API_HASH")
    bot_token: str = Field(default="7906812572:AAH9zQ2qNo6xfbtYJgAp__IgpZ6NagOUKVU", env="BOT_TOKEN")
    session_string: str = Field(
        default="AQF479MAOfQsgQqBSTecm4wrC_-2pgI5FXpCU_xOvLpG-b8NvUQy6OjZnMuxSE-6ycZdxEVdccFXvYqJQLuoBVDtXdn5G6DYPqrDxz5V-ArKcmE_pImLXboXRBJTz7gNjypy3eGuukw-Y4N0_D5GvcMrtH01sOX_mXg7y5mt63d8UegpdJCYFo9cKsnqc5dWDdDlQh3gmMfaCn8FeUXmYLmyCIbmW8mZFkme3U4V79xj2FtCkz-l0ftbfG394ewG-J0igfMABMnGpRbdrAjz1p88RNZO74xGuahEtVscf2-RZzIBrR9pttQp-vt5Gr5uqcy5TgHN9WPP9K_ybvFk-XUucbNJRwAAAAG5oVUxAA",
        env="SESSION_STRING"
    )
    
    # Database Configuration
    mongodb_uri: str = Field(default="", env="MONGODB_URI")
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    use_memory_db: bool = Field(default=True, env="USE_MEMORY_DB")
    
    # Music Service APIs
    spotify_client_id: str = Field(default="", env="SPOTIFY_CLIENT_ID")
    spotify_client_secret: str = Field(default="", env="SPOTIFY_CLIENT_SECRET")
    soundcloud_client_id: str = Field(default="", env="SOUNDCLOUD_CLIENT_ID")
    genius_api_key: str = Field(default="", env="GENIUS_API_KEY")  # For lyrics
    
    # Bot Features Configuration
    max_playlist_size: int = Field(default=500, env="MAX_PLAYLIST_SIZE")
    max_queue_size: int = Field(default=100, env="MAX_QUEUE_SIZE")
    max_download_size_mb: int = Field(default=50, env="MAX_DOWNLOAD_SIZE_MB")
    auto_leave_minutes: int = Field(default=5, env="AUTO_LEAVE_MINUTES")
    
    # Audio Quality Settings
    default_audio_quality: str = Field(default="high", env="DEFAULT_AUDIO_QUALITY")  # low, medium, high, best
    audio_bitrate: int = Field(default=320, env="AUDIO_BITRATE")
    enable_equalizer: bool = Field(default=True, env="ENABLE_EQUALIZER")
    
    # Performance Settings
    download_workers: int = Field(default=4, env="DOWNLOAD_WORKERS")
    cache_size_mb: int = Field(default=500, env="CACHE_SIZE_MB")
    enable_compression: bool = Field(default=True, env="ENABLE_COMPRESSION")
    connection_pool_size: int = Field(default=50, env="CONNECTION_POOL_SIZE")
    
    # UI/UX Settings
    language_default: str = Field(default="en", env="LANGUAGE_DEFAULT")
    supported_languages: List[str] = Field(
        default=["en", "es", "fr", "de", "ru", "pt", "hi", "ar", "zh", "ja"],
        env="SUPPORTED_LANGUAGES"
    )
    show_lyrics: bool = Field(default=True, env="SHOW_LYRICS")
    show_thumbnails: bool = Field(default=True, env="SHOW_THUMBNAILS")
    
    # Admin Configuration
    admin_ids: List[int] = Field(default=[], env="ADMIN_IDS")
    sudo_users: List[int] = Field(default=[], env="SUDO_USERS")
    broadcast_limit: int = Field(default=50, env="BROADCAST_LIMIT")
    
    # Rate Limiting
    rate_limit_commands: int = Field(default=5, env="RATE_LIMIT_COMMANDS")  # per minute
    rate_limit_downloads: int = Field(default=10, env="RATE_LIMIT_DOWNLOADS")  # per hour
    
    # Paths
    base_dir: str = Field(default=os.path.dirname(os.path.abspath(__file__)), env="BASE_DIR")
    download_dir: str = Field(default="downloaded_songs", env="DOWNLOAD_DIR")
    cache_dir: str = Field(default="cache", env="CACHE_DIR")
    logs_dir: str = Field(default="logs", env="LOGS_DIR")
    
    # Feature Flags
    enable_spotify: bool = Field(default=True, env="ENABLE_SPOTIFY")
    enable_soundcloud: bool = Field(default=True, env="ENABLE_SOUNDCLOUD")
    enable_radio: bool = Field(default=True, env="ENABLE_RADIO")
    enable_voice_commands: bool = Field(default=False, env="ENABLE_VOICE_COMMANDS")
    enable_analytics: bool = Field(default=True, env="ENABLE_ANALYTICS")
    enable_collaborative_playlists: bool = Field(default=True, env="ENABLE_COLLABORATIVE")
    
    # Analytics & Monitoring
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# Audio quality presets
AUDIO_QUALITY_PRESETS = {
    "low": {
        "format": "bestaudio[abr<=96]/best[abr<=96]/bestaudio/best",
        "bitrate": 96,
        "sample_rate": 22050
    },
    "medium": {
        "format": "bestaudio[abr<=192]/best[abr<=192]/bestaudio/best",
        "bitrate": 192,
        "sample_rate": 44100
    },
    "high": {
        "format": "bestaudio[abr<=320]/best[abr<=320]/bestaudio/best",
        "bitrate": 320,
        "sample_rate": 48000
    },
    "best": {
        "format": "bestaudio/best",
        "bitrate": 320,
        "sample_rate": 48000
    }
}

# Equalizer presets
EQUALIZER_PRESETS = {
    "flat": {"bands": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]},
    "bass_boost": {"bands": [6, 4, 2, 0, -1, -1, 0, 1, 2, 3]},
    "treble_boost": {"bands": [-1, -1, 0, 1, 2, 3, 4, 5, 6, 7]},
    "vocal": {"bands": [-2, -1, 0, 2, 4, 4, 3, 1, 0, -1]},
    "rock": {"bands": [5, 3, -1, -1, 1, 3, 5, 6, 6, 6]},
    "pop": {"bands": [-1, 0, 2, 4, 4, 2, 0, -1, -1, -1]},
    "jazz": {"bands": [4, 2, 0, 2, 4, 4, 2, 0, 0, 0]},
    "classical": {"bands": [0, 0, 0, 0, 0, 0, -2, -2, -2, -4]}
}

# Language translations (sample)
TRANSLATIONS = {
    "en": {
        "welcome": "🎵 Welcome to the Enhanced Music Bot!",
        "help": "Here are the available commands:",
        "now_playing": "🎶 Now Playing",
        "added_to_queue": "✅ Added to queue",
        "queue_empty": "📭 Queue is empty",
        "error": "❌ An error occurred",
        "searching": "🔍 Searching...",
        "downloading": "⬇️ Downloading...",
        "playing": "▶️ Playing",
        "paused": "⏸️ Paused",
        "resumed": "▶️ Resumed",
        "stopped": "⏹️ Stopped",
        "left_vc": "👋 Left voice chat",
        "joined_vc": "🎤 Joined voice chat"
    },
    # Add more languages as needed
}

# Radio stations
RADIO_STATIONS = {
    "pop": [
        {"name": "Top 40", "url": "http://stream.example.com/top40"},
        {"name": "Hit Radio", "url": "http://stream.example.com/hits"},
    ],
    "rock": [
        {"name": "Rock FM", "url": "http://stream.example.com/rock"},
        {"name": "Classic Rock", "url": "http://stream.example.com/classic"},
    ],
    "jazz": [
        {"name": "Smooth Jazz", "url": "http://stream.example.com/jazz"},
        {"name": "Jazz FM", "url": "http://stream.example.com/jazzfm"},
    ],
    "classical": [
        {"name": "Classical FM", "url": "http://stream.example.com/classical"},
        {"name": "Symphony", "url": "http://stream.example.com/symphony"},
    ],
    "electronic": [
        {"name": "EDM", "url": "http://stream.example.com/edm"},
        {"name": "Techno", "url": "http://stream.example.com/techno"},
    ]
}

# Create config instance
config = BotConfig()

# Create directories if they don't exist
for directory in [config.download_dir, config.cache_dir, config.logs_dir]:
    os.makedirs(os.path.join(config.base_dir, directory), exist_ok=True)

# Export for backward compatibility
API_ID = config.api_id
API_HASH = config.api_hash
BOT_TOKEN = config.bot_token
SESSION_STRING = config.session_string
MONGODB_URI = config.mongodb_uri
MAX_PLAYLIST_SIZE = config.max_playlist_size
