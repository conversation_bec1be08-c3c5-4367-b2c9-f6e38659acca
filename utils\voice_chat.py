"""
Advanced voice chat management for the Telegram Music Bot
Using the latest py-tgcalls library
"""
import os
import asyncio
import logging
from typing import Dict, Any, Optional, Callable, Tuple, Union
import aiofiles

from pytgcalls import PyTgCalls
from pytgcalls.types import Update
from pytgcalls.types.input_stream import InputAudioStream, AudioParameters
from pytgcalls.exceptions import NoActiveGroupCall, GroupCallNotFound

from config import FFMPEG_PATH, FFPROBE_PATH

logger = logging.getLogger("musicbot.voice_chat")

# Set FFmpeg paths
FFMPEG_PATH = FFMPEG_PATH
FFPROBE_PATH = FFPROBE_PATH

# Check if FFmpeg is installed
ffmpeg_installed = os.path.exists(FFMPEG_PATH)
ffprobe_installed = os.path.exists(FFPROBE_PATH)

# Path to silence file for joining voice chats
SILENCE_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "silence.mp3")

# Wait time after joining a voice chat
JOIN_WAIT_TIME = 2

# Global PyTgCalls instance
py_tgcalls = None

# Track active voice chats
active_chats = {}

# Queue workers
queue_workers = {}

# Callback for now playing notifications
now_playing_callbacks = {}

async def initialize_tgcalls(user_client) -> PyTgCalls:
    """Initialize PyTgCalls with the user client"""
    global py_tgcalls
    
    if py_tgcalls is None:
        py_tgcalls = PyTgCalls(user_client)
        
        # Set up event handlers
        py_tgcalls.on_stream_end(on_stream_end_handler)
        py_tgcalls.on_participants_change(on_participants_change_handler)
        
        await py_tgcalls.start()
        
    return py_tgcalls

async def on_stream_end_handler(_, update: Update):
    """Handle stream end event"""
    chat_id = update.chat_id
    
    logger.info(f"Stream ended in chat {chat_id}")
    
    if chat_id in active_chats:
        # Get the queue for this chat
        queue = active_chats[chat_id]["queue"]
        
        if queue:
            # Play the next song in queue
            next_song = queue.pop(0)
            await play_audio(chat_id, next_song["audio_file"], next_song["title"])
            
            # Notify about now playing
            if chat_id in now_playing_callbacks:
                callback = now_playing_callbacks[chat_id]
                await callback(f"🎵 Now playing: {next_song['title']}")
        else:
            # No more songs in queue
            logger.info(f"Queue empty for chat {chat_id}")
            
            # Keep the bot in the voice chat
            await play_silence(chat_id)

async def on_participants_change_handler(_, update: Update):
    """Handle participants change event"""
    chat_id = update.chat_id
    
    logger.info(f"Participants changed in chat {chat_id}")
    
    # If the bot is the only participant, leave the voice chat
    if update.participants == 1 and chat_id in active_chats:
        logger.info(f"Bot is alone in voice chat {chat_id}, leaving")
        await leave_voice_chat(None, chat_id)

async def join_voice_chat(client, chat_id: int) -> Tuple[bool, str]:
    """Join a voice chat in a specific chat"""
    global py_tgcalls
    
    # Check if FFmpeg and FFprobe are installed
    if not ffmpeg_installed or not ffprobe_installed:
        return False, "❌ FFmpeg and/or FFprobe are not installed. Run download_ffmpeg.py to download them automatically."
    
    if py_tgcalls is None:
        py_tgcalls = await initialize_tgcalls(client)
    
    # Check if already in this voice chat
    if chat_id in active_chats:
        return True, "✅ Already in the voice chat"
    
    try:
        # Initialize chat state
        active_chats[chat_id] = {
            "playing": False,
            "paused": False,
            "current_song": None,
            "queue": []
        }
        
        logger.info(f"Attempting to join voice chat in {chat_id}")
        
        # Join the voice chat by playing silence
        await play_silence(chat_id)
        
        # Wait a moment to ensure we've joined
        await asyncio.sleep(JOIN_WAIT_TIME)
        
        return True, "✅ Successfully joined the voice chat"
    except NoActiveGroupCall:
        if chat_id in active_chats:
            del active_chats[chat_id]
        return False, "❌ No active voice chat found. Please start a voice chat first."
    except GroupCallNotFound:
        if chat_id in active_chats:
            del active_chats[chat_id]
        return False, "❌ Group call not found. Please start a voice chat first."
    except Exception as e:
        logger.error(f"Error joining voice chat: {str(e)}")
        if chat_id in active_chats:
            del active_chats[chat_id]
        return False, f"❌ Failed to join voice chat: {str(e)}"

async def play_silence(chat_id: int) -> bool:
    """Play silence to join or stay in a voice chat"""
    global py_tgcalls
    
    if py_tgcalls is None:
        return False
    
    try:
        await py_tgcalls.play(chat_id, InputAudioStream(
            SILENCE_FILE,
            AudioParameters(
                bitrate=48000,
            ),
        ))
        return True
    except Exception as e:
        logger.error(f"Error playing silence: {str(e)}")
        return False

async def play_audio(chat_id: int, audio_file: str, title: str) -> bool:
    """Play an audio file in a voice chat"""
    global py_tgcalls
    
    if py_tgcalls is None:
        return False
    
    try:
        # Update chat state
        if chat_id in active_chats:
            active_chats[chat_id]["playing"] = True
            active_chats[chat_id]["paused"] = False
            active_chats[chat_id]["current_song"] = {
                "title": title,
                "audio_file": audio_file
            }
        
        # Ensure file path is absolute
        if not os.path.isabs(audio_file):
            audio_file = os.path.abspath(audio_file)
        
        logger.info(f"Playing audio in chat {chat_id}: {audio_file}")
        
        # Play the audio
        await py_tgcalls.play(chat_id, InputAudioStream(
            audio_file,
            AudioParameters(
                bitrate=48000,
            ),
        ))
        
        return True
    except Exception as e:
        logger.error(f"Error playing audio: {str(e)}")
        return False

async def play_in_voice_chat(client, chat_id: int, audio_file: str, title: str, 
                            notify_callback: Optional[Callable] = None) -> Tuple[bool, str]:
    """Play audio in a voice chat or add to queue if something is already playing"""
    global py_tgcalls, queue_workers
    
    # Check if FFmpeg and FFprobe are installed
    if not ffmpeg_installed or not ffprobe_installed:
        return False, "❌ FFmpeg and/or FFprobe are not installed. Run download_ffmpeg.py to download them automatically."
    
    if py_tgcalls is None:
        py_tgcalls = await initialize_tgcalls(client)
    
    try:
        # Check if we're already in the voice chat
        if chat_id not in active_chats:
            # Join the voice chat first
            join_success, join_message = await join_voice_chat(client, chat_id)
            if not join_success:
                return False, f"Failed to join voice chat: {join_message}"
        
        # Store the callback for now playing notifications
        if notify_callback:
            now_playing_callbacks[chat_id] = notify_callback
        
        # Check if something is already playing
        if chat_id in active_chats and active_chats[chat_id].get("playing", False):
            # Add to queue
            active_chats[chat_id]["queue"].append({
                "title": title,
                "audio_file": audio_file
            })
            
            return True, f"🎵 Added to queue: {title}"
        
        # Nothing is playing, play this song
        success = await play_audio(chat_id, audio_file, title)
        
        if success:
            # Notify about now playing
            if notify_callback:
                await notify_callback(f"🎵 Now playing: {title}")
            
            return True, f"🎵 Now playing: {title}"
        else:
            return False, "❌ Failed to play audio"
    except Exception as e:
        logger.error(f"Error in play_in_voice_chat: {str(e)}")
        return False, f"❌ Error: {str(e)}"

async def pause_voice_chat(client, chat_id: int) -> Tuple[bool, str]:
    """Pause the playback in a voice chat"""
    global py_tgcalls
    
    if py_tgcalls is None or chat_id not in active_chats:
        return False, "❌ Not currently in a voice chat"
    
    try:
        await py_tgcalls.pause(chat_id)
        
        # Update state
        active_chats[chat_id]["paused"] = True
        
        return True, "⏸️ Playback paused"
    except Exception as e:
        logger.error(f"Error pausing playback: {str(e)}")
        return False, f"❌ Error pausing playback: {str(e)}"

async def resume_voice_chat(client, chat_id: int) -> Tuple[bool, str]:
    """Resume the paused playback in a voice chat"""
    global py_tgcalls
    
    if py_tgcalls is None or chat_id not in active_chats:
        return False, "❌ Not currently in a voice chat"
    
    try:
        await py_tgcalls.resume(chat_id)
        
        # Update state
        active_chats[chat_id]["paused"] = False
        
        return True, "▶️ Playback resumed"
    except Exception as e:
        logger.error(f"Error resuming playback: {str(e)}")
        return False, f"❌ Error resuming playback: {str(e)}"

async def skip_current_song(client, chat_id: int) -> Tuple[bool, str]:
    """Skip the current song and play the next one in queue"""
    global py_tgcalls
    
    if py_tgcalls is None or chat_id not in active_chats:
        return False, "❌ Not currently in a voice chat"
    
    try:
        # Check if there are songs in queue
        if not active_chats[chat_id]["queue"]:
            # No songs in queue, just stop the current one
            await py_tgcalls.stop(chat_id)
            
            # Update state
            active_chats[chat_id]["playing"] = False
            active_chats[chat_id]["current_song"] = None
            
            # Play silence to stay in the voice chat
            await play_silence(chat_id)
            
            return True, "⏹️ Playback stopped (no more songs in queue)"
        
        # Get the next song from queue
        next_song = active_chats[chat_id]["queue"].pop(0)
        
        # Play the next song
        success = await play_audio(chat_id, next_song["audio_file"], next_song["title"])
        
        if success:
            # Notify about now playing
            if chat_id in now_playing_callbacks:
                callback = now_playing_callbacks[chat_id]
                await callback(f"🎵 Now playing: {next_song['title']}")
            
            return True, f"⏭️ Skipped to: {next_song['title']}"
        else:
            return False, "❌ Failed to play next song"
    except Exception as e:
        logger.error(f"Error skipping song: {str(e)}")
        return False, f"❌ Error skipping song: {str(e)}"

async def leave_voice_chat(client, chat_id: int) -> Tuple[bool, str]:
    """Leave a voice chat"""
    global py_tgcalls, queue_workers
    
    if py_tgcalls is None:
        return False, "❌ Voice chat system not initialized"
    
    try:
        # Check if we're in the voice chat
        if chat_id not in active_chats:
            return False, "❌ Not in the voice chat"
        
        # Leave the voice chat
        await py_tgcalls.leave(chat_id)
        
        # Clean up
        if chat_id in active_chats:
            del active_chats[chat_id]
        
        if chat_id in now_playing_callbacks:
            del now_playing_callbacks[chat_id]
        
        return True, "👋 Left the voice chat"
    except Exception as e:
        logger.error(f"Error leaving voice chat: {str(e)}")
        
        # Clean up anyway
        if chat_id in active_chats:
            del active_chats[chat_id]
        
        if chat_id in now_playing_callbacks:
            del now_playing_callbacks[chat_id]
        
        return False, f"❌ Error leaving voice chat: {str(e)}"

def get_queue(chat_id: int) -> list:
    """Get the current queue for a chat"""
    if chat_id in active_chats:
        return active_chats[chat_id].get("queue", [])
    return []

def get_current_song(chat_id: int) -> Optional[Dict[str, Any]]:
    """Get the current song for a chat"""
    if chat_id in active_chats:
        return active_chats[chat_id].get("current_song")
    return None

async def clear_queue(chat_id: int) -> bool:
    """Clear the queue for a chat"""
    if chat_id in active_chats:
        active_chats[chat_id]["queue"] = []
        return True
    return False

async def remove_from_queue(chat_id: int, index: int) -> Optional[Dict[str, Any]]:
    """Remove a song from the queue by index"""
    if chat_id in active_chats and 0 <= index < len(active_chats[chat_id]["queue"]):
        return active_chats[chat_id]["queue"].pop(index)
    return None
