"""
SoundCloud Service - Integration with SoundCloud API
"""

import asyncio
from typing import Dict, List, Optional
from loguru import logger
import aiohttp
import re
from datetime import datetime

class SoundCloudService:
    """Service for interacting with SoundCloud"""
    
    def __init__(self, client_id: str = None):
        """Initialize SoundCloud service"""
        self.client_id = client_id
        self.base_url = "https://api-v2.soundcloud.com"
        self.enabled = bool(client_id)
        
        if not self.enabled:
            logger.warning("SoundCloud service disabled - missing client ID")
    
    async def get_track_info(self, url: str) -> Optional[Dict]:
        """Get track information from SoundCloud URL"""
        if not self.enabled:
            return None
        
        try:
            # Resolve the URL to get track info
            async with aiohttp.ClientSession() as session:
                resolve_url = f"{self.base_url}/resolve?url={url}&client_id={self.client_id}"
                
                async with session.get(resolve_url) as response:
                    if response.status != 200:
                        logger.error(f"SoundCloud API error: {response.status}")
                        return None
                    
                    track = await response.json()
            
            # Format response
            return {
                "title": f"{track.get('user', {}).get('username', 'Unknown')} - {track.get('title', 'Unknown')}",
                "artist": track.get('user', {}).get('username', 'Unknown'),
                "duration": track.get('duration', 0) // 1000,  # Convert to seconds
                "thumbnail": track.get('artwork_url') or track.get('user', {}).get('avatar_url'),
                "url": track.get('permalink_url', url),
                "soundcloud_id": track.get('id'),
                "stream_url": f"{track.get('stream_url')}?client_id={self.client_id}" if track.get('stream_url') else None,
                "genre": track.get('genre'),
                "plays": track.get('playback_count', 0),
                "likes": track.get('likes_count', 0),
                "waveform_url": track.get('waveform_url')
            }
            
        except Exception as e:
            logger.error(f"Error getting SoundCloud track info: {e}")
            return None
    
    async def search(self, query: str, limit: int = 10) -> Optional[List[Dict]]:
        """Search for tracks on SoundCloud"""
        if not self.enabled:
            return None
        
        try:
            async with aiohttp.ClientSession() as session:
                search_url = f"{self.base_url}/search/tracks"
                params = {
                    'q': query,
                    'client_id': self.client_id,
                    'limit': limit
                }
                
                async with session.get(search_url, params=params) as response:
                    if response.status != 200:
                        logger.error(f"SoundCloud search error: {response.status}")
                        return None
                    
                    data = await response.json()
            
            tracks = []
            for track in data.get('collection', []):
                tracks.append({
                    "title": f"{track.get('user', {}).get('username', 'Unknown')} - {track.get('title', 'Unknown')}",
                    "artist": track.get('user', {}).get('username', 'Unknown'),
                    "duration": track.get('duration', 0) // 1000,
                    "thumbnail": track.get('artwork_url') or track.get('user', {}).get('avatar_url'),
                    "url": track.get('permalink_url'),
                    "soundcloud_id": track.get('id'),
                    "genre": track.get('genre'),
                    "plays": track.get('playback_count', 0)
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error searching SoundCloud: {e}")
            return None
    
    async def get_playlist_tracks(self, url: str) -> Optional[List[Dict]]:
        """Get all tracks from a SoundCloud playlist"""
        if not self.enabled:
            return None
        
        try:
            # Resolve the playlist URL
            async with aiohttp.ClientSession() as session:
                resolve_url = f"{self.base_url}/resolve?url={url}&client_id={self.client_id}"
                
                async with session.get(resolve_url) as response:
                    if response.status != 200:
                        return None
                    
                    playlist = await response.json()
            
            tracks = []
            for track in playlist.get('tracks', []):
                tracks.append({
                    "title": f"{track.get('user', {}).get('username', 'Unknown')} - {track.get('title', 'Unknown')}",
                    "artist": track.get('user', {}).get('username', 'Unknown'),
                    "duration": track.get('duration', 0) // 1000,
                    "url": track.get('permalink_url'),
                    "soundcloud_id": track.get('id')
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error getting SoundCloud playlist: {e}")
            return None
    
    async def get_user_tracks(self, user_url: str, limit: int = 20) -> Optional[List[Dict]]:
        """Get tracks from a SoundCloud user"""
        if not self.enabled:
            return None
        
        try:
            # Resolve user URL
            async with aiohttp.ClientSession() as session:
                resolve_url = f"{self.base_url}/resolve?url={user_url}&client_id={self.client_id}"
                
                async with session.get(resolve_url) as response:
                    if response.status != 200:
                        return None
                    
                    user = await response.json()
                
                # Get user tracks
                tracks_url = f"{self.base_url}/users/{user['id']}/tracks"
                params = {
                    'client_id': self.client_id,
                    'limit': limit
                }
                
                async with session.get(tracks_url, params=params) as response:
                    if response.status != 200:
                        return None
                    
                    data = await response.json()
            
            tracks = []
            for track in data.get('collection', []):
                tracks.append({
                    "title": f"{user.get('username', 'Unknown')} - {track.get('title', 'Unknown')}",
                    "artist": user.get('username', 'Unknown'),
                    "duration": track.get('duration', 0) // 1000,
                    "thumbnail": track.get('artwork_url') or user.get('avatar_url'),
                    "url": track.get('permalink_url'),
                    "soundcloud_id": track.get('id'),
                    "created_at": track.get('created_at')
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error getting user tracks: {e}")
            return None
    
    async def get_trending_tracks(self, genre: str = None, limit: int = 20) -> Optional[List[Dict]]:
        """Get trending tracks from SoundCloud"""
        if not self.enabled:
            return None
        
        try:
            async with aiohttp.ClientSession() as session:
                charts_url = f"{self.base_url}/charts"
                params = {
                    'kind': 'trending',
                    'genre': f'soundcloud:genres:{genre}' if genre else 'soundcloud:genres:all-music',
                    'client_id': self.client_id,
                    'limit': limit
                }
                
                async with session.get(charts_url, params=params) as response:
                    if response.status != 200:
                        return None
                    
                    data = await response.json()
            
            tracks = []
            for item in data.get('collection', []):
                track = item.get('track', {})
                tracks.append({
                    "title": f"{track.get('user', {}).get('username', 'Unknown')} - {track.get('title', 'Unknown')}",
                    "artist": track.get('user', {}).get('username', 'Unknown'),
                    "duration": track.get('duration', 0) // 1000,
                    "thumbnail": track.get('artwork_url') or track.get('user', {}).get('avatar_url'),
                    "url": track.get('permalink_url'),
                    "soundcloud_id": track.get('id'),
                    "genre": track.get('genre'),
                    "plays": track.get('playback_count', 0)
                })
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error getting trending tracks: {e}")
            return None
    
    async def download_track(self, track_url: str, output_path: str) -> Optional[str]:
        """Download a track from SoundCloud"""
        if not self.enabled:
            return None
        
        try:
            # Get track info
            track_info = await self.get_track_info(track_url)
            if not track_info or not track_info.get('stream_url'):
                return None
            
            # Download the track
            async with aiohttp.ClientSession() as session:
                async with session.get(track_info['stream_url']) as response:
                    if response.status != 200:
                        return None
                    
                    # Save to file
                    with open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
            
            logger.info(f"Downloaded SoundCloud track: {track_info['title']}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error downloading SoundCloud track: {e}")
            return None
    
    def extract_track_id(self, url: str) -> Optional[str]:
        """Extract track ID from SoundCloud URL"""
        # Pattern for soundcloud.com/user/track URLs
        pattern = r'soundcloud\.com/([^/]+)/([^/\?]+)'
        match = re.search(pattern, url)
        
        if match:
            return f"{match.group(1)}/{match.group(2)}"
        
        return None
