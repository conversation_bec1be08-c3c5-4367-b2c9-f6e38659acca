"""
Music download and playback utilities for the Telegram Music Bot
"""

import os
import tempfile
import yt_dlp
from .yt import stream_from_url

# Store active downloads
active_downloads = {}

async def download_audio(url_or_query):
    """Download audio from a URL or query"""
    try:
        # Get stream info
        stream_info = stream_from_url(url_or_query)
        if not stream_info:
            return False, "Could not find audio source", None

        stream_url = stream_info['stream_url']
        title = stream_info['title']

        # Download the audio
        temp_dir = tempfile.gettempdir()
        audio_file = os.path.join(temp_dir, f"{title}.mp3")

        ydl_opts = {
            'format': 'bestaudio/best',
            'outtmpl': audio_file,
            # Uncomment and modify the line below if you've installed FFmpeg but it's not in PATH
            # 'ffmpeg_location': 'C:/ffmpeg/bin/ffmpeg.exe',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([stream_url])

        return True, title, audio_file
    except Exception as e:
        return False, f"Error downloading audio: {str(e)}", None

def search_youtube(query, max_results=5):
    """Search for videos on YouTube"""
    try:
        ydl_opts = {
            'format': 'bestaudio/best',
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'default_search': f'ytsearch{max_results}',
            # Uncomment and modify the line below if you've installed FFmpeg but it's not in PATH
            # 'ffmpeg_location': 'C:/ffmpeg/bin/ffmpeg.exe',
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            result = ydl.extract_info(f"ytsearch{max_results}:{query}", download=False)

        if 'entries' not in result or not result['entries']:
            return False, "No results found", None

        entries = []
        for entry in result['entries']:
            title = entry.get('title', 'Unknown Title')
            video_id = entry.get('id', '')
            url = f"https://www.youtube.com/watch?v={video_id}"
            duration = entry.get('duration', 0)
            thumbnail = entry.get('thumbnail', None)

            entries.append({
                'title': title,
                'url': url,
                'video_id': video_id,
                'duration': duration,
                'thumbnail': thumbnail
            })

        return True, "Search successful", entries
    except Exception as e:
        return False, f"Error searching YouTube: {str(e)}", None
