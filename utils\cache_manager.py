"""
Cache Manager - Redis-based caching for performance optimization
"""

import asyncio
import json
import pickle
from typing import Any, Optional, Union
from datetime import timedelta
import redis.asyncio as redis
from loguru import logger
import hashlib

class CacheManager:
    """Manage caching with Redis for improved performance"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        """Initialize cache manager"""
        self.redis_url = redis_url
        self.redis_client = None
        self.enabled = False
        self.default_ttl = 3600  # 1 hour default
        
    async def connect(self):
        """Connect to Redis"""
        try:
            self.redis_client = await redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            await self.redis_client.ping()
            self.enabled = True
            logger.info("Connected to Redis cache")
        except Exception as e:
            logger.warning(f"Redis connection failed, caching disabled: {e}")
            self.enabled = False
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis_client:
            await self.redis_client.close()
            self.enabled = False
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if not self.enabled:
            return None
        
        try:
            value = await self.redis_client.get(key)
            if value:
                # Try to deserialize JSON
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    # Return as string if not JSON
                    return value
            return None
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """Set value in cache"""
        if not self.enabled:
            return False
        
        try:
            # Serialize to JSON if possible
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            elif not isinstance(value, str):
                value = str(value)
            
            # Set with TTL
            if ttl is None:
                ttl = self.default_ttl
            
            await self.redis_client.setex(key, ttl, value)
            return True
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        if not self.enabled:
            return False
        
        try:
            await self.redis_client.delete(key)
            return True
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        if not self.enabled:
            return False
        
        try:
            return await self.redis_client.exists(key) > 0
        except Exception as e:
            logger.error(f"Cache exists error: {e}")
            return False
    
    async def get_or_set(
        self,
        key: str,
        factory_func,
        ttl: Optional[int] = None
    ) -> Optional[Any]:
        """Get from cache or compute and set"""
        # Try to get from cache first
        value = await self.get(key)
        if value is not None:
            return value
        
        # Compute value
        try:
            if asyncio.iscoroutinefunction(factory_func):
                value = await factory_func()
            else:
                value = factory_func()
            
            # Cache the result
            if value is not None:
                await self.set(key, value, ttl)
            
            return value
        except Exception as e:
            logger.error(f"Error in get_or_set: {e}")
            return None
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern"""
        if not self.enabled:
            return 0
        
        try:
            keys = []
            async for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                await self.redis_client.delete(*keys)
            
            return len(keys)
        except Exception as e:
            logger.error(f"Error clearing pattern: {e}")
            return 0
    
    # Specific cache methods for different data types
    
    async def cache_user_preference(
        self,
        user_id: int,
        preference: str,
        value: Any,
        ttl: int = 86400  # 24 hours
    ) -> bool:
        """Cache user preference"""
        key = f"user:{user_id}:pref:{preference}"
        return await self.set(key, value, ttl)
    
    async def get_user_preference(
        self,
        user_id: int,
        preference: str
    ) -> Optional[Any]:
        """Get user preference from cache"""
        key = f"user:{user_id}:pref:{preference}"
        return await self.get(key)
    
    async def cache_search_results(
        self,
        query: str,
        service: str,
        results: list,
        ttl: int = 3600  # 1 hour
    ) -> bool:
        """Cache search results"""
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        key = f"search:{service}:{query_hash}"
        return await self.set(key, results, ttl)
    
    async def get_search_results(
        self,
        query: str,
        service: str
    ) -> Optional[list]:
        """Get search results from cache"""
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        key = f"search:{service}:{query_hash}"
        return await self.get(key)
    
    async def cache_track_info(
        self,
        track_id: str,
        service: str,
        info: dict,
        ttl: int = 7200  # 2 hours
    ) -> bool:
        """Cache track information"""
        key = f"track:{service}:{track_id}"
        return await self.set(key, info, ttl)
    
    async def get_track_info(
        self,
        track_id: str,
        service: str
    ) -> Optional[dict]:
        """Get track information from cache"""
        key = f"track:{service}:{track_id}"
        return await self.get(key)
    
    async def cache_download_url(
        self,
        track_id: str,
        url: str,
        ttl: int = 1800  # 30 minutes
    ) -> bool:
        """Cache download URL"""
        key = f"download:url:{track_id}"
        return await self.set(key, url, ttl)
    
    async def get_download_url(self, track_id: str) -> Optional[str]:
        """Get download URL from cache"""
        key = f"download:url:{track_id}"
        return await self.get(key)
    
    async def increment_play_count(
        self,
        track_id: str,
        user_id: Optional[int] = None
    ) -> int:
        """Increment play count for a track"""
        if not self.enabled:
            return 0
        
        try:
            key = f"stats:plays:{track_id}"
            count = await self.redis_client.incr(key)
            
            # Also track user plays
            if user_id:
                user_key = f"stats:user:{user_id}:plays"
                await self.redis_client.incr(user_key)
            
            return count
        except Exception as e:
            logger.error(f"Error incrementing play count: {e}")
            return 0
    
    async def get_stats(self, stat_type: str = "plays") -> dict:
        """Get statistics from cache"""
        if not self.enabled:
            return {}
        
        try:
            stats = {}
            pattern = f"stats:{stat_type}:*"
            
            async for key in self.redis_client.scan_iter(match=pattern):
                value = await self.redis_client.get(key)
                if value:
                    # Extract ID from key
                    parts = key.split(":")
                    if len(parts) >= 3:
                        item_id = parts[2]
                        stats[item_id] = int(value)
            
            return stats
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {}
    
    async def rate_limit_check(
        self,
        user_id: int,
        action: str,
        limit: int,
        window: int = 60  # seconds
    ) -> bool:
        """Check rate limit for user action"""
        if not self.enabled:
            return True  # Allow if cache is disabled
        
        try:
            key = f"ratelimit:{user_id}:{action}"
            
            # Increment counter
            count = await self.redis_client.incr(key)
            
            # Set expiry on first increment
            if count == 1:
                await self.redis_client.expire(key, window)
            
            return count <= limit
        except Exception as e:
            logger.error(f"Rate limit check error: {e}")
            return True  # Allow on error
