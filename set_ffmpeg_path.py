"""
Set FFmpeg paths in the system PATH temporarily for the current session
"""

import os
import sys
import subprocess

# FFmpeg paths
FFMPEG_DIR = "ffmpeg"
FFMPEG_BIN_DIR = os.path.join(FFMPEG_DIR, "bin")
FFMPEG_PATH = os.path.join(FFMPEG_BIN_DIR, "ffmpeg.exe")
FFPROBE_PATH = os.path.join(FFMPEG_BIN_DIR, "ffprobe.exe")

def check_ffmpeg_exists():
    """Check if FFmpeg exists in the specified directory"""
    if not os.path.exists(FFMPEG_DIR):
        print(f"❌ FFmpeg directory not found: {FFMPEG_DIR}")
        print("Please run download_ffmpeg.py first to download FFmpeg.")
        return False
    
    if not os.path.exists(FFMPEG_PATH):
        print(f"❌ FFmpeg executable not found: {FFMPEG_PATH}")
        print("Please run download_ffmpeg.py first to download FFmpeg.")
        return False
    
    if not os.path.exists(FFPROBE_PATH):
        print(f"❌ FFprobe executable not found: {FFPROBE_PATH}")
        print("Please run download_ffmpeg.py first to download FFmpeg.")
        return False
    
    return True

def set_path():
    """Add FFmpeg bin directory to the system PATH"""
    if not check_ffmpeg_exists():
        return False
    
    # Get the absolute path to the FFmpeg bin directory
    ffmpeg_bin_abs_path = os.path.abspath(FFMPEG_BIN_DIR)
    
    # Add to PATH
    current_path = os.environ.get('PATH', '')
    
    # Check if already in PATH
    if ffmpeg_bin_abs_path in current_path:
        print(f"✅ FFmpeg bin directory is already in PATH: {ffmpeg_bin_abs_path}")
        return True
    
    # Add to PATH
    os.environ['PATH'] = f"{ffmpeg_bin_abs_path}{os.pathsep}{current_path}"
    
    print(f"✅ Added FFmpeg bin directory to PATH: {ffmpeg_bin_abs_path}")
    
    # Test if FFmpeg is now accessible
    try:
        result = subprocess.run(
            ["ffmpeg", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        version = result.stdout.decode().split('\n')[0]
        print(f"✅ FFmpeg is now accessible: {version}")
        return True
    except (subprocess.SubprocessError, FileNotFoundError) as e:
        print(f"❌ Error accessing FFmpeg: {e}")
        return False

if __name__ == "__main__":
    if set_path():
        print("✅ FFmpeg paths set successfully!")
        print("You can now run the bot with:")
        print("python main.py")
    else:
        print("❌ Failed to set FFmpeg paths.")
        print("Please run download_ffmpeg.py first to download FFmpeg.")
