"""
Radio Service - Stream radio stations and online radio
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional
from loguru import logger
import json
import m3u8

class RadioService:
    """Service for streaming radio stations"""
    
    def __init__(self):
        """Initialize radio service"""
        self.radio_browser_api = "https://de1.api.radio-browser.info/json"
        self.session = None
        
        # Popular radio stations
        self.popular_stations = {
            "BBC Radio 1": {
                "url": "http://stream.live.vc.bbcmedia.co.uk/bbc_radio_one",
                "genre": "Pop",
                "country": "UK",
                "language": "English"
            },
            "NPR": {
                "url": "https://npr-ice.streamguys1.com/live.mp3",
                "genre": "News/Talk",
                "country": "USA",
                "language": "English"
            },
            "Radio Paradise": {
                "url": "https://stream.radioparadise.com/aac-320",
                "genre": "Eclectic",
                "country": "USA",
                "language": "English"
            },
            "Classical FM": {
                "url": "http://media-ice.musicradio.com/ClassicFMMP3",
                "genre": "Classical",
                "country": "UK",
                "language": "English"
            },
            "Jazz FM": {
                "url": "http://edge-bauermz-01-cr.sharp-stream.com/jazzfm.mp3",
                "genre": "Jazz",
                "country": "UK",
                "language": "English"
            }
        }
    
    async def search_stations(
        self,
        query: str = None,
        country: str = None,
        language: str = None,
        genre: str = None,
        limit: int = 20
    ) -> List[Dict]:
        """Search for radio stations"""
        try:
            params = {
                "limit": limit,
                "order": "clickcount",
                "reverse": "true"
            }
            
            # Build search parameters
            if query:
                params["name"] = query
            if country:
                params["country"] = country
            if language:
                params["language"] = language
            if genre:
                params["tag"] = genre
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.radio_browser_api}/stations/search"
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        logger.error(f"Radio API error: {response.status}")
                        return []
                    
                    stations = await response.json()
            
            # Format results
            results = []
            for station in stations:
                results.append({
                    "name": station.get("name", "Unknown"),
                    "url": station.get("url_resolved") or station.get("url"),
                    "country": station.get("country", "Unknown"),
                    "language": station.get("language", "Unknown"),
                    "genre": station.get("tags", ""),
                    "bitrate": station.get("bitrate", 0),
                    "votes": station.get("votes", 0),
                    "favicon": station.get("favicon"),
                    "homepage": station.get("homepage"),
                    "codec": station.get("codec", "Unknown")
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching radio stations: {e}")
            return []
    
    async def get_top_stations(self, limit: int = 20) -> List[Dict]:
        """Get top radio stations by popularity"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.radio_browser_api}/stations/topclick/{limit}"
                async with session.get(url) as response:
                    if response.status != 200:
                        return []
                    
                    stations = await response.json()
            
            results = []
            for station in stations:
                results.append({
                    "name": station.get("name", "Unknown"),
                    "url": station.get("url_resolved") or station.get("url"),
                    "country": station.get("country", "Unknown"),
                    "language": station.get("language", "Unknown"),
                    "genre": station.get("tags", ""),
                    "bitrate": station.get("bitrate", 0),
                    "clicks": station.get("clickcount", 0),
                    "favicon": station.get("favicon")
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting top stations: {e}")
            return []
    
    async def get_stations_by_genre(self, genre: str, limit: int = 20) -> List[Dict]:
        """Get radio stations by genre"""
        return await self.search_stations(genre=genre, limit=limit)
    
    async def get_stations_by_country(self, country: str, limit: int = 20) -> List[Dict]:
        """Get radio stations by country"""
        return await self.search_stations(country=country, limit=limit)
    
    async def get_station_stream_url(self, station_url: str) -> Optional[str]:
        """Get the actual stream URL from a station URL"""
        try:
            # Check if it's already a direct stream
            if any(ext in station_url.lower() for ext in ['.mp3', '.aac', '.ogg', '.opus']):
                return station_url
            
            # Check if it's an m3u/m3u8 playlist
            if '.m3u' in station_url.lower():
                return await self._parse_m3u_playlist(station_url)
            
            # Try to get redirect URL
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    station_url,
                    allow_redirects=True,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    # Check content type
                    content_type = response.headers.get('content-type', '').lower()
                    
                    if 'audio' in content_type:
                        return str(response.url)
                    
                    # Check if it's a playlist
                    if 'mpegurl' in content_type or 'x-scpls' in content_type:
                        content = await response.text()
                        return await self._parse_playlist_content(content, response.url)
            
            # If all else fails, return original URL
            return station_url
            
        except Exception as e:
            logger.error(f"Error getting stream URL: {e}")
            return station_url
    
    async def _parse_m3u_playlist(self, playlist_url: str) -> Optional[str]:
        """Parse M3U/M3U8 playlist"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(playlist_url) as response:
                    if response.status != 200:
                        return None
                    
                    content = await response.text()
            
            # Parse M3U content
            lines = content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and ('http' in line or '://' in line):
                    return line
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing M3U playlist: {e}")
            return None
    
    async def _parse_playlist_content(self, content: str, base_url: str) -> Optional[str]:
        """Parse playlist content (M3U, PLS, etc.)"""
        try:
            lines = content.strip().split('\n')
            
            # Try to find stream URLs
            for line in lines:
                line = line.strip()
                
                # M3U format
                if line and not line.startswith('#') and ('http' in line or '://' in line):
                    return line
                
                # PLS format
                if line.startswith('File') and '=' in line:
                    url = line.split('=', 1)[1].strip()
                    if url:
                        return url
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing playlist content: {e}")
            return None
    
    async def get_genres(self) -> List[str]:
        """Get list of available genres"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.radio_browser_api}/tags"
                params = {"order": "stationcount", "reverse": "true", "limit": 50}
                
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        return []
                    
                    tags = await response.json()
            
            # Extract genre names
            genres = [tag['name'] for tag in tags if tag.get('stationcount', 0) > 10]
            return genres
            
        except Exception as e:
            logger.error(f"Error getting genres: {e}")
            return []
    
    async def get_countries(self) -> List[str]:
        """Get list of countries with radio stations"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.radio_browser_api}/countries"
                params = {"order": "stationcount", "reverse": "true", "limit": 100}
                
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        return []
                    
                    countries = await response.json()
            
            # Extract country names
            country_list = [
                country['name'] 
                for country in countries 
                if country.get('stationcount', 0) > 5
            ]
            return country_list
            
        except Exception as e:
            logger.error(f"Error getting countries: {e}")
            return []
    
    def get_popular_stations_list(self) -> List[Dict]:
        """Get list of popular curated stations"""
        stations = []
        for name, info in self.popular_stations.items():
            stations.append({
                "name": name,
                "url": info["url"],
                "genre": info["genre"],
                "country": info["country"],
                "language": info["language"]
            })
        return stations
    
    async def validate_stream_url(self, url: str) -> bool:
        """Validate if a stream URL is working"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.head(
                    url,
                    allow_redirects=True,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    # Check if status is OK and content type is audio
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '').lower()
                        return 'audio' in content_type or 'mpeg' in content_type
            
            return False
            
        except Exception:
            return False
