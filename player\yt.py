"""
YouTube streaming module for the Telegram VC Music Bot
"""

import yt_dlp
import re
import requests

def is_youtube_url(url):
    """Check if the URL is a valid YouTube URL"""
    youtube_regex = r'(https?://)?(www\.)?(youtube|youtu|youtube-nocookie)\.(com|be)/(watch\?v=|embed/|v/|.+\?v=)?([^&=%\?]{11})'
    return bool(re.match(youtube_regex, url))

def is_spotify_url(url):
    """Check if the URL is a valid Spotify URL"""
    spotify_regex = r'https?://open\.spotify\.com/(track|album|playlist)/[a-zA-Z0-9]+'
    return bool(re.match(spotify_regex, url))

def is_direct_audio_url(url):
    """Check if the URL is a direct audio file URL"""
    audio_extensions = ['.mp3', '.m4a', '.wav', '.flac', '.aac', '.ogg']
    return any(url.lower().endswith(ext) for ext in audio_extensions)

def get_youtube_url_from_query(query):
    """Search YouTube and get the first result URL"""
    with yt_dlp.YoutubeDL({'quiet': True, 'format': 'bestaudio', 'noplaylist': True}) as ydl:
        result = ydl.extract_info(f"ytsearch1:{query}", download=False)
        if 'entries' in result and result['entries']:
            return f"https://www.youtube.com/watch?v={result['entries'][0]['id']}"
    return None

def stream_from_url(url_or_query):
    """Get audio stream URL from YouTube URL, Spotify URL, direct audio URL, or search query"""
    # If it's a direct audio URL, return it directly
    if is_direct_audio_url(url_or_query):
        return url_or_query

    # If it's not a YouTube URL, try to search for it
    if not is_youtube_url(url_or_query):
        if is_spotify_url(url_or_query):
            # Extract track name from Spotify and search on YouTube
            # This is a simplified version, a real implementation would use Spotify API
            track_name = url_or_query.split('/')[-1].replace('-', ' ')
            url_or_query = get_youtube_url_from_query(track_name)
        else:
            # Treat as a search query
            url_or_query = get_youtube_url_from_query(url_or_query)

    if not url_or_query:
        return None

    # Extract audio URL from YouTube
    ydl_opts = {
        'format': 'bestaudio',
        'quiet': True,
        'noplaylist': True,
        'extract_flat': False,
        # Uncomment and modify the line below if you've installed FFmpeg but it's not in PATH
        # 'ffmpeg_location': 'C:/ffmpeg/bin/ffmpeg.exe',
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url_or_query, download=False)
            return {
                'stream_url': info.get('url'),
                'title': info.get('title', 'Unknown Title'),
                'duration': info.get('duration', 0),
                'thumbnail': info.get('thumbnail', None)
            }
    except Exception as e:
        print(f"Error extracting info: {e}")
        return None
