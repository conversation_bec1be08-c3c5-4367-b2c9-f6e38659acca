"""
Create a silent MP3 file for use with PyTgCalls
"""

import numpy as np
from scipy.io.wavfile import write
import subprocess
import os

# Create a silent WAV file
def create_silence_wav(filename="silence.wav", duration=5, sample_rate=48000):
    """Create a silent WAV file"""
    # Create a silent audio array (all zeros)
    audio = np.zeros(int(duration * sample_rate), dtype=np.int16)
    
    # Write to WAV file
    write(filename, sample_rate, audio)
    print(f"Created {filename}")
    return filename

# Convert WAV to MP3 using FFmpeg if available
def convert_to_mp3(wav_file="silence.wav", mp3_file="silence.mp3"):
    """Convert WAV to MP3 using FFmpeg"""
    try:
        # Try to use FFmpeg for conversion
        subprocess.run(
            ["ffmpeg", "-i", wav_file, "-codec:a", "libmp3lame", "-qscale:a", "2", mp3_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        print(f"Converted to {mp3_file}")
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        print("FFmpeg not found. Cannot convert to MP3.")
        return False

# Create a dummy MP3 file without FFmpeg
def create_dummy_mp3(filename="silence.mp3"):
    """Create a dummy MP3 file with minimal header"""
    # This is a minimal valid MP3 file (not actually silent, but very short)
    mp3_data = bytes.fromhex(
        "FFFB9064000420000000000000000000000000000000000000000000000000"
        "0000000000000000000000000000000000000000000000000000000000000000"
    )
    
    with open(filename, "wb") as f:
        f.write(mp3_data)
    
    print(f"Created dummy {filename}")
    return filename

if __name__ == "__main__":
    # First try the proper way with FFmpeg
    wav_file = create_silence_wav()
    success = convert_to_mp3(wav_file)
    
    # If FFmpeg is not available, create a dummy MP3
    if not success:
        create_dummy_mp3()
    
    # Clean up WAV file
    if os.path.exists(wav_file):
        os.remove(wav_file)
