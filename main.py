"""
Telegram Music Bot
A bot that can download and send music from YouTube and join voice chats
"""

import os
import sys
import asyncio
import tempfile
import yt_dlp
import logging

# --- Logging setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
)
logger = logging.getLogger('musicbot.main')

# --- Ensure ffmpeg/ffprobe are in PATH for all subprocesses ---
ffmpeg_bin_dir = os.path.abspath(os.path.join("ffmpeg", "bin"))
os.environ["PATH"] = ffmpeg_bin_dir + os.pathsep + os.environ.get("PATH", "")
os.environ["FFMPEG_BINARY"] = os.path.join(ffmpeg_bin_dir, "ffmpeg.exe")
os.environ["FFPROBE_BINARY"] = os.path.join(ffmpeg_bin_dir, "ffprobe.exe")
# ------------------------------------------------------------
from pyrogram import Client, filters, idle
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from pyrogram.enums import ParseMode
from config import API_ID, API_HASH, BOT_TOKEN, SESSION_STRING
from player.yt import stream_from_url
from playlist_handlers import register_playlist_handlers, song_hash_to_title

# Store active downloads and queues
active_tasks = {}
active_downloads = {}

# Minimum download percentage before we can start playing
MIN_PLAYABLE_PERCENTAGE = 5  # 5% of the file

def handle_download_progress(d, title, temp_file, final_path):
    """Handle download progress updates and enable progressive playback"""
    if d['status'] == 'downloading':
        # Calculate download percentage
        if 'total_bytes' in d and d['total_bytes'] > 0:
            percentage = (d['downloaded_bytes'] / d['total_bytes']) * 100
        elif 'total_bytes_estimate' in d and d['total_bytes_estimate'] > 0:
            percentage = (d['downloaded_bytes'] / d['total_bytes_estimate']) * 100
        else:
            percentage = 0
            
        # Update the active_downloads entry
        if title in active_downloads:
            active_downloads[title]['progress'] = percentage
            active_downloads[title]['temp_file'] = temp_file
            
            # Check for the actual file (yt-dlp might add an extra .mp3 extension)
            actual_temp_file = temp_file
            if not os.path.exists(actual_temp_file):
                # Try with additional extensions
                possible_extensions = [".mp3", ".part", ".webm", ".m4a"]
                for ext in possible_extensions:
                    test_path = temp_file + ext
                    if os.path.exists(test_path):
                        actual_temp_file = test_path
                        print(f"Found actual temp file with extension: {actual_temp_file}")
                        break
            
            # Update the temp file in active_downloads
            active_downloads[title]['temp_file'] = actual_temp_file
            
            # Make the file available for playback once it reaches the minimum percentage
            if percentage >= MIN_PLAYABLE_PERCENTAGE and os.path.exists(actual_temp_file):
                # Create a copy to make it accessible for playback
                partial_playable_path = f"{final_path}.partial"
                try:
                    # Use a more robust file copying approach
                    import shutil
                    # Only copy if the file has grown or doesn't exist yet
                    should_copy = False
                    
                    if not os.path.exists(partial_playable_path):
                        should_copy = True
                        print(f"Creating new partial file: {partial_playable_path}")
                    elif os.path.getsize(actual_temp_file) > os.path.getsize(partial_playable_path):
                        should_copy = True
                        print(f"Updating partial file with newer content")
                        
                    if should_copy:
                        try:
                            # First try to copy directly
                            shutil.copy2(actual_temp_file, partial_playable_path)
                            print(f"Direct copy successful: {actual_temp_file} -> {partial_playable_path}")
                        except Exception as e1:
                            print(f"Direct copy failed: {e1}, trying with intermediate file")
                            # If direct copy fails, try with an intermediate file
                            intermediate_path = f"{partial_playable_path}.tmp"
                            try:
                                shutil.copy2(actual_temp_file, intermediate_path)
                                # Then rename the intermediate file
                                if os.path.exists(partial_playable_path):
                                    os.remove(partial_playable_path)
                                os.rename(intermediate_path, partial_playable_path)
                                print(f"Intermediate copy successful")
                            except Exception as e2:
                                print(f"Intermediate copy failed: {e2}")
                except Exception as e:
                    print(f"Error creating partial playable file: {e}")
    
    elif d['status'] == 'finished':
        # Download is complete
        if title in active_downloads:
            active_downloads[title]['progress'] = 100
            print(f"Download finished for {title}")
            
            # Print information about the downloaded file
            print(f"Downloaded file info - temp_file: {temp_file}")
            if os.path.exists(temp_file):
                print(f"  - Size: {os.path.getsize(temp_file)} bytes")
            else:
                print(f"  - File does not exist at expected path")
                # Check for file with additional extensions
                possible_extensions = [".mp3", ".part", ".webm", ".m4a"]
                for ext in possible_extensions:
                    test_path = temp_file + ext
                    if os.path.exists(test_path):
                        print(f"  - Found at {test_path} with size {os.path.getsize(test_path)} bytes")
            
            # Clean up any partial playable files
            partial_playable_path = f"{final_path}.partial"
            if os.path.exists(partial_playable_path):
                print(f"Partial playable file exists: {partial_playable_path} with size {os.path.getsize(partial_playable_path)} bytes")
                # Don't delete immediately - wait for the final file to be ready
                # This ensures we always have a playable file
                try:
                    # Check if the final file exists and is valid
                    if os.path.exists(final_path) and os.path.getsize(final_path) > 0:
                        print(f"Final file exists: {final_path} with size {os.path.getsize(final_path)} bytes")
                        # Try to remove the partial file, but don't worry if it fails
                        try:
                            os.remove(partial_playable_path)
                            print(f"Removed partial playable file after successful download")
                        except Exception as e:
                            print(f"Non-critical: Error removing partial playable file: {e}")
                    else:
                        print(f"Final file does not exist or is empty: {final_path}")
                except Exception as e:
                    print(f"Error checking final file: {e}")

# Note about voice chat functionality
VOICE_CHAT_NOTE = """
🎵 **Voice Chat Functionality:**

The bot can join voice chats and play music! Here's how to use it:

1. Add the bot to a group
2. Start a voice chat in the group
3. Use `/play <song name or URL>` to play music directly
4. Use `/queue` to see the songs in the queue
5. Use `/pause`, `/resume`, and `/leave` to control playback

⚠️ **Important Requirements:**
FFmpeg and FFprobe are required for voice chat functionality.

If you see an error about FFmpeg or FFprobe not being installed, simply run:
```
python download_ffmpeg.py
```

This will automatically download and set up FFmpeg for you!
"""

# Initialize clients with unique session names to avoid conflicts
import time
current_time = int(time.time())

# Extend Client class with invoke_callback_data method
class MusicBotClient(Client):
    async def invoke_callback_data(self, message, callback_data):
        """Helper method to simulate a callback query with the given data"""
        # Instead of creating a fake callback query, just update the message directly
        try:
            # For playback controls
            if callback_data == "playback_controls":
                from player.voice_chat import active_chats, is_playing
                
                is_currently_playing = await is_playing(message.chat.id)
                current_track = None
                
                if message.chat.id in active_chats and active_chats[message.chat.id].get('current_track'):
                    current_track = active_chats[message.chat.id]['current_track']
                    
                if current_track and is_currently_playing:
                    # Calculate elapsed time
                    import time
                    elapsed = time.time() - current_track.get('start_time', 0)
                    duration = current_track.get('duration', 0)
                    
                    # Format time
                    if duration > 0:
                        elapsed_mins, elapsed_secs = divmod(int(elapsed), 60)
                        total_mins, total_secs = divmod(int(duration), 60)
                        time_str = f"({elapsed_mins}:{elapsed_secs:02d}/{total_mins}:{total_secs:02d})"
                    else:
                        time_str = ""
                        
                    controls_text = (
                        f"🎵 <b>Now Playing:</b> {current_track['title']}\n"
                        f"⏱️ {time_str}\n\n"
                        f"Use the controls below to manage playback:"
                    )
                    
                    # Create playback control buttons
                    buttons = [
                        [
                            InlineKeyboardButton("⏮️ Prev", callback_data="prev_song"),
                            InlineKeyboardButton("⏸️ Pause", callback_data="pause_playback"),
                            InlineKeyboardButton("⏭️ Next", callback_data="skip_song")
                        ],
                        [
                            InlineKeyboardButton("🔄 Refresh", callback_data="playback_controls"),
                            InlineKeyboardButton("🎶 Queue", callback_data="show_queue")
                        ],
                        [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
                    ]
                else:
                    controls_text = (
                        "⏹️ <b>Nothing Playing</b>\n\n"
                        "There's no music playing right now. Use /play to start playing music!"
                    )
                    
                    # Create simplified buttons when nothing is playing
                    buttons = [
                        [
                            InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play "),
                            InlineKeyboardButton("🎶 Queue", callback_data="show_queue")
                        ],
                        [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
                    ]
                    
                await message.edit_text(controls_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
                
            # For queue display
            elif callback_data == "show_queue":
                from player.voice_chat import get_queue, active_chats
                queue = await get_queue(message.chat.id)
                
                # Get current playing track if any
                current_track = None
                if message.chat.id in active_chats and active_chats[message.chat.id].get('current_track'):
                    current_track = active_chats[message.chat.id]['current_track']
                
                # Build queue text with current track and queue
                if current_track:
                    # Calculate elapsed time
                    import time
                    elapsed = time.time() - current_track.get('start_time', 0)
                    duration = current_track.get('duration', 0)
                    
                    # Format time
                    if duration > 0:
                        elapsed_mins, elapsed_secs = divmod(int(elapsed), 60)
                        total_mins, total_secs = divmod(int(duration), 60)
                        time_str = f" ({elapsed_mins}:{elapsed_secs:02d}/{total_mins}:{total_secs:02d})"
                    else:
                        time_str = ""
                        
                    queue_text = f"🎵 <b>Now Playing:</b>\n• {current_track['title']}{time_str}\n\n"
                    
                    if queue:
                        queue_text += "<b>Up Next:</b>\n"
                    else:
                        queue_text += "<i>Queue is empty. Add more songs!</i>\n"
                else:
                    queue_text = "🔄 <b>Nothing playing currently</b>\n\n"
                    if queue:
                        queue_text += "<b>Songs in Queue:</b>\n"
                    else:
                        queue_text += "<i>Queue is empty. Add songs with /play!</i>\n"
                
                # Add queue items
                for idx, song in enumerate(queue, 1):
                    queue_text += f"{idx}. {song['title']}\n"
                
                # Create buttons for queue management
                buttons = []
                
                # Add buttons for each song in queue
                for idx, song in enumerate(queue, 1):
                    title_short = song['title'][:25] + ("..." if len(song['title']) > 25 else "")
                    buttons.append([
                        InlineKeyboardButton(f"{idx}. {title_short}", callback_data=f"queue_info_{idx}"),
                        InlineKeyboardButton("❌", callback_data=f"queue_remove_{idx}")
                    ])
                
                # Add queue control buttons
                control_buttons = []
                if current_track:
                    control_buttons = [
                        InlineKeyboardButton("⏸️ Pause", callback_data="pause_playback"),
                        InlineKeyboardButton("⏭️ Skip", callback_data="skip_song")
                    ]
                
                if control_buttons:
                    buttons.append(control_buttons)
                    
                # Add navigation buttons
                buttons.append([
                    InlineKeyboardButton("🔄 Refresh", callback_data="show_queue"),
                    InlineKeyboardButton("⬅️ Back", callback_data="main_menu")
                ])
                
                await message.edit_text(queue_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
                
            # For main menu
            elif callback_data == "main_menu":
                # Create a more organized and visually appealing menu
                menu_buttons = [
                    # Music playback controls
                    [
                        InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play "),
                        InlineKeyboardButton("⏯️ Controls", callback_data="playback_controls")
                    ],
                    # Queue management
                    [
                        InlineKeyboardButton("🎶 Queue", callback_data="show_queue"),
                        InlineKeyboardButton("🔄 Skip", callback_data="skip_song")
                    ],
                    # Playlist management
                    [
                        InlineKeyboardButton("📂 My Playlists", callback_data="show_playlists"),
                        InlineKeyboardButton("➕ New Playlist", callback_data="create_playlist_prompt")
                    ],
                    # Additional options
                    [
                        InlineKeyboardButton("⚙️ Settings", callback_data="settings"),
                        InlineKeyboardButton("ℹ️ Help", callback_data="about")
                    ]
                ]
                
                # Check if we're in a group chat
                is_group = message.chat.type in ["group", "supergroup"]
                
                # Add voice chat controls if in a group
                if is_group:
                    voice_chat_buttons = [
                        InlineKeyboardButton("🎤 Join Voice", callback_data="join_voice_chat"),
                        InlineKeyboardButton("👋 Leave Voice", callback_data="leave_voice_chat")
                    ]
                    menu_buttons.insert(1, voice_chat_buttons)
                
                # Send a more informative welcome message
                welcome_text = (
                    "🎧 <b>TG Music Bot</b> 🎧\n\n"
                    "Play music in voice chats, create playlists, and more!\n\n"
                    "<b>Quick Commands:</b>\n"
                    "• /play <song> - Play a song\n"
                    "• /queue - Show current queue\n"
                    "• /pause - Pause playback\n"
                    "• /resume - Resume playback\n"
                    "• /skip - Skip to next song\n\n"
                    "Use the buttons below for easy access to all features:"
                )
                
                # Send the menu image with the welcome text
                try:
                    from config import BASE_DIR
                    menu_image_path = os.path.join(BASE_DIR, "images", "menu.png")
                    
                    if os.path.exists(menu_image_path):
                        # Delete the previous message and send a new one with the image
                        await message.delete()
                        
                        # Send the new message with image
                        await self.send_photo(
                            chat_id=message.chat.id,
                            photo=menu_image_path,
                            caption=welcome_text,
                            reply_markup=InlineKeyboardMarkup(menu_buttons),
                            parse_mode=ParseMode.HTML
                        )
                    else:
                        # If image doesn't exist, just update the text
                        await message.edit_text(
                            welcome_text,
                            reply_markup=InlineKeyboardMarkup(menu_buttons),
                            parse_mode=ParseMode.HTML
                        )
                except Exception as e:
                    logger.error(f"Error sending menu image: {e}")
                    # Fallback to text-only menu
                    await message.edit_text(
                        welcome_text,
                        reply_markup=InlineKeyboardMarkup(menu_buttons),
                        parse_mode=ParseMode.HTML
                    )
                
            else:
                # For other callback data, just update the message with a generic message
                await message.edit_text(
                    "🔄 Action completed. Use the buttons below to continue:",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("🎶 Show Queue", callback_data="show_queue")],
                        [InlineKeyboardButton("⬅️ Back to Menu", callback_data="main_menu")]
                    ]),
                    parse_mode=ParseMode.HTML
                )
        except Exception as e:
            logger.error(f"Error in invoke_callback_data: {e}")
            # If there's an error, show a generic error message
            try:
                await message.edit_text(
                    f"❌ An error occurred: {str(e)}\n\nPlease try again.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("⬅️ Back to Menu", callback_data="main_menu")]]),
                    parse_mode=ParseMode.HTML
                )
            except:
                pass

# Create client instances
bot = MusicBotClient(f"music_bot_session_{current_time}", api_id=API_ID, api_hash=API_HASH, bot_token=BOT_TOKEN)
user = Client(f"user_session_string_{current_time}", api_id=API_ID, api_hash=API_HASH, session_string=SESSION_STRING)

# Playlist management commands
import json

@bot.on_message(filters.command("createplaylist"))
async def create_playlist_command(_, message: Message):
    """Create a new playlist (robust command parsing)"""
    try:
        text = message.text or ""
        # Remove leading bot mention if present
        if text.startswith("@"):
            text = " ".join(text.split()[1:])
        # Remove the command itself (with or without bot mention)
        parts = text.split(maxsplit=1)
        if len(parts) < 2 or not parts[1].strip():
            await message.reply("❌ Please provide a valid name for the playlist: /createplaylist <name>")
            return
        playlist_name = parts[1].strip()
        playlists_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), 'playlists'))
        if not os.path.exists(playlists_dir):
            try:
                os.makedirs(playlists_dir)
            except Exception as e:
                await message.reply(f"❌ Failed to create playlists directory: {e}")
                return
        playlist_path = os.path.join(playlists_dir, f"{playlist_name}.json")
        if os.path.exists(playlist_path):
            await message.reply(f"❌ Playlist '{playlist_name}' already exists.")
            return
        try:
            with open(playlist_path, 'w', encoding='utf-8') as f:
                json.dump([], f)
            await message.reply(f"✅ Playlist '{playlist_name}' created at {playlist_path}.")
        except Exception as e:
            await message.reply(f"❌ Failed to create playlist file: {e}\nPath: {playlist_path}")
            logger.error(f"Failed to create playlist: {e}")
    except Exception as e:
        logger.error(f"Error in create playlist command: {e}")

async def show_queue_menu(client, chat_id, message=None):
    try:
        # ... (existing code to get queue_text and queue)
        # Build the inline keyboard dynamically based on queue
        buttons = []
        for idx, song in enumerate(queue, 1):
            buttons.append([InlineKeyboardButton(f"{idx}. {song['title'][:30]}" + ("..." if len(song['title']) > 30 else ""), callback_data=f"queue_{idx}")])
        buttons.append([InlineKeyboardButton("⬅️ Back", callback_data="main_menu")])
        if message:
            await message.edit_text(
                queue_text,
                reply_markup=InlineKeyboardMarkup(buttons)
            )
        else:
            await client.send_message(
                chat_id,
                queue_text,
                reply_markup=InlineKeyboardMarkup(buttons)
            )
        logger.info(f"Queue menu shown to chat {chat_id}")
    except Exception as e:
        logger.error(f"Error showing queue menu: {e}")
        if message:
            await message.edit_text("❌ Failed to load queue menu.")
        else:
            await client.send_message(chat_id, "❌ Failed to load queue menu.")

async def show_playlists_menu(client, chat_id, message=None):
    try:
        # ... (existing code to get playlists_text and playlists)
        # Build the inline keyboard dynamically based on playlists
        buttons = []
        for name in playlists:
            buttons.append([InlineKeyboardButton(f"🎶 {name[:30]}" + ("..." if len(name) > 30 else ""), callback_data=f"pl_{hash(name) & 0xFFFFFFFF}")])
        buttons.append([InlineKeyboardButton("⬅️ Back", callback_data="main_menu")])
        if message:
            await message.edit_text(
                playlists_text,
                reply_markup=InlineKeyboardMarkup(buttons)
            )
        else:
            await client.send_message(
                chat_id,
                playlists_text,
                reply_markup=InlineKeyboardMarkup(buttons)
            )
        logger.info(f"Playlists menu shown to chat {chat_id}")
    except Exception as e:
        logger.error(f"Error showing playlists menu: {e}")
        if message:
            await message.edit_text("❌ Failed to load playlists menu.")
        else:
            await client.send_message(chat_id, "❌ Failed to load playlists menu.")

@bot.on_message(filters.command("start"))
async def start_command(client, message):
    try:
        logger.info(f"/start command from user {message.from_user.id}")
        
        # Create a more organized and visually appealing menu
        menu_buttons = [
            # Music playback controls
            [
                InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play "),
                InlineKeyboardButton("⏯️ Controls", callback_data="playback_controls")
            ],
            # Queue management
            [
                InlineKeyboardButton("🎶 Queue", callback_data="show_queue"),
                InlineKeyboardButton("🔄 Skip", callback_data="skip_song")
            ],
            # Playlist management
            [
                InlineKeyboardButton("📂 My Playlists", callback_data="show_playlists"),
                InlineKeyboardButton("➕ New Playlist", callback_data="create_playlist_prompt")
            ],
            # Additional options
            [
                InlineKeyboardButton("⚙️ Settings", callback_data="settings"),
                InlineKeyboardButton("ℹ️ Help", callback_data="about")
            ]
        ]
        
        # Check if we're in a group chat
        is_group = message.chat.type in ["group", "supergroup"]
        
        # Add voice chat controls if in a group
        if is_group:
            voice_chat_buttons = [
                InlineKeyboardButton("🎤 Join Voice", callback_data="join_voice_chat"),
                InlineKeyboardButton("👋 Leave Voice", callback_data="leave_voice_chat")
            ]
            menu_buttons.insert(1, voice_chat_buttons)
        
        # Send a more informative welcome message
        welcome_text = (
            "🎧 <b>TG Music Bot</b> 🎧\n\n"
            "Play music in voice chats, create playlists, and more!\n\n"
            "<b>Quick Commands:</b>\n"
            "• /play <song> - Play a song\n"
            "• /queue - Show current queue\n"
            "• /pause - Pause playback\n"
            "• /resume - Resume playback\n"
            "• /skip - Skip to next song\n\n"
            "Use the buttons below for easy access to all features:"
        )
        
        # Send the menu image with the welcome text
        try:
            from config import BASE_DIR
            menu_image_path = os.path.join(BASE_DIR, "images", "menu.png")
            
            if os.path.exists(menu_image_path):
                # Send the message with image
                await message.reply_photo(
                    photo=menu_image_path,
                    caption=welcome_text,
                    reply_markup=InlineKeyboardMarkup(menu_buttons),
                    parse_mode=ParseMode.HTML
                )
            else:
                # If image doesn't exist, just send text
                await message.reply(
                    welcome_text,
                    reply_markup=InlineKeyboardMarkup(menu_buttons),
                    parse_mode=ParseMode.HTML
                )
        except Exception as e:
            logger.error(f"Error sending menu image: {e}")
            # Fallback to text-only menu
            await message.reply(
                welcome_text,
                reply_markup=InlineKeyboardMarkup(menu_buttons),
                parse_mode=ParseMode.HTML
            )
    except Exception as e:
        logger.error(f"Error in /start command: {e}")
        await message.reply("❌ An error occurred while showing the menu.")

# --- Global song hash -> title mapping for Add to Playlist buttons ---
song_hash_to_title = {}

@bot.on_message(filters.command("play"))
async def play_command(_, message: Message):
    """Handle the /play command - now joins VC and plays the song directly"""
    if len(message.command) < 2:
        await message.reply("❌ Please provide a song name or URL after /play")
        return

    # Get the song query
    query = message.text.split(None, 1)[1]
    chat_id = message.chat.id

    # Send processing message
    process_msg = await message.reply("🔄 Processing...")

    # Get stream info
    stream_info = stream_from_url(query)
    if not stream_info:
        await process_msg.edit("❌ Could not find audio source")
        return

    title = stream_info['title']
    stream_url = stream_info['stream_url']
    thumbnail = stream_info.get('thumbnail')
    duration = stream_info.get('duration', 0)

    # Check if duration is too long (>10 minutes)
    if duration > 600:
        await process_msg.edit("❌ Song is too long (>10 minutes). Please choose a shorter song.")
        return

    # Create notification callback for queue updates
    async def notify_callback(text):
        try:
            await message.reply(text)
        except Exception as e:
            print(f"Error in notify callback: {e}")
    
    # Try to play in voice chat
    try:
        from player.voice_chat import play_in_voice_chat
        from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        import re
        songs_dir = os.path.join(os.path.dirname(__file__), 'downloaded_songs')
        if not os.path.exists(songs_dir):
            os.makedirs(songs_dir)
        safe_title = re.sub(r'[^\w\-_\. ]', '_', title)
        audio_file = os.path.join(songs_dir, f"{safe_title}.mp3")
        audio_file_mp3mp3 = audio_file + ".mp3"
        # Use cached file if it exists (handle yt-dlp double extension)
        if os.path.exists(audio_file):
            stream_url_to_use = audio_file
        elif os.path.exists(audio_file_mp3mp3):
            stream_url_to_use = audio_file_mp3mp3
        else:
            stream_url_to_use = stream_url
            import threading
            def download_song_bg():
                # Use a temporary filename during download to avoid conflicts
                temp_dir = os.path.join(songs_dir, "temp")
                if not os.path.exists(temp_dir):
                    os.makedirs(temp_dir)
                
                # Generate a unique temporary filename
                import uuid
                temp_filename = os.path.join(temp_dir, f"{uuid.uuid4().hex}.mp3")
                
                ydl_opts = {
                    'format': 'bestaudio/best',
                    'outtmpl': temp_filename,
                    'postprocessors': [{
                        'key': 'FFmpegExtractAudio',
                        'preferredcodec': 'mp3',
                        'preferredquality': '192',
                    }],
                    # Add progress hooks for partial playback
                    'progress_hooks': [lambda d: handle_download_progress(d, title, temp_filename, audio_file)],
                    # Fix file access issues
                    'noprogress': False,
                    'quiet': False,
                    'no_warnings': False,
                    # Don't use .part files (which cause access issues)
                    'part': False,
                    # Retry on errors
                    'retries': 5,
                    'fragment_retries': 5,
                    # Avoid file access conflicts
                    'windowsfilenames': True,
                    'ignoreerrors': True,
                    # Don't add extension to already specified extension
                    'nooverwrites': True,
                    # Don't add postprocessor extensions
                    'postprocessor_args': ['-y'],
                    # Keep original file
                    'keepvideo': False
                }
                
                try:
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        ydl.download([stream_url])
                    
                    # After successful download, move the file to its final location
                    # Use a retry mechanism to handle potential file access issues
                    max_retries = 5
                    
                    # Check for the actual downloaded file (yt-dlp might add an extra .mp3 extension)
                    actual_temp_filename = temp_filename
                    if not os.path.exists(actual_temp_filename) and os.path.exists(temp_filename + ".mp3"):
                        actual_temp_filename = temp_filename + ".mp3"
                        print(f"Found downloaded file with extra extension: {actual_temp_filename}")
                    
                    for retry in range(max_retries):
                        try:
                            # Check if the final file already exists (from another download)
                            if os.path.exists(audio_file):
                                # If it does, we don't need to move our temp file
                                try:
                                    if os.path.exists(actual_temp_filename):
                                        os.remove(actual_temp_filename)
                                except Exception as e:
                                    print(f"Failed to remove temp file: {e}")
                                break
                            
                            # Check if the source file exists
                            if not os.path.exists(actual_temp_filename):
                                print(f"Source file not found: {actual_temp_filename}")
                                # Try to find the file with different extensions
                                possible_extensions = [".mp3", ".mp3.mp3", ".webm", ".m4a"]
                                for ext in possible_extensions:
                                    test_path = temp_filename + ext
                                    if os.path.exists(test_path):
                                        actual_temp_filename = test_path
                                        print(f"Found alternative source file: {actual_temp_filename}")
                                        break
                            
                            # Move the temp file to the final location if it exists
                            if os.path.exists(actual_temp_filename):
                                import shutil
                                print(f"Moving {actual_temp_filename} to {audio_file}")
                                shutil.move(actual_temp_filename, audio_file)
                                print(f"Successfully moved file to {audio_file}")
                                break
                            else:
                                print(f"Source file still not found after searching alternatives")
                                if retry < max_retries - 1:
                                    # Wait longer before retrying
                                    import time
                                    time.sleep(2)
                        except Exception as e:
                            print(f"Error during file move (attempt {retry+1}/{max_retries}): {e}")
                            if retry < max_retries - 1:
                                # Wait before retrying
                                import time
                                time.sleep(2)
                            else:
                                print(f"Failed to move downloaded file after {max_retries} attempts: {e}")
                except Exception as e:
                    print(f"Error downloading song: {e}")
            
            # Start the download in a background thread
            threading.Thread(target=download_song_bg, daemon=True).start()
            
            # Mark this song as being downloaded
            active_downloads[title] = {
                'started_at': time.time(),
                'temp_file': None,  # Will be updated by progress hook
                'progress': 0,
                'final_path': audio_file
            }
        # Store hash->title mapping for Add to Playlist
        song_hash = hash(title) & 0xFFFFFFFF
        song_hash_to_title[song_hash] = title
        async def now_playing_callback(text):
            try:
                # Create rich now playing message with thumbnail
                message_text = f"🎵 <b>Now Playing:</b> {title}"
                
                # Only send a single standalone message for now playing (with thumbnail if available)
                if thumbnail:
                    try:
                        await bot.send_photo(
                            chat_id=message.chat.id,
                            photo=thumbnail,
                            caption=f"🎵 <b>Now Playing:</b> {title}",
                            reply_markup=InlineKeyboardMarkup([
                                [InlineKeyboardButton("➕ Add to Playlist", callback_data=f"addpl_{song_hash}")],
                                [InlineKeyboardButton("⬅️ Main Menu", callback_data="main_menu")]
                            ]),
                            parse_mode=ParseMode.HTML
                        )
                    except Exception as e:
                        logger.error(f"Error sending thumbnail: {e}")
                        await bot.send_message(
                            chat_id=message.chat.id,
                            text=f"🎵 <b>Now Playing:</b> {title}",
                            reply_markup=InlineKeyboardMarkup([
                                [InlineKeyboardButton("➕ Add to Playlist", callback_data=f"addpl_{song_hash}")],
                                [InlineKeyboardButton("⬅️ Main Menu", callback_data="main_menu")]
                            ]),
                            parse_mode=ParseMode.HTML
                        )
                else:
                    await bot.send_message(
                        chat_id=message.chat.id,
                        text=f"🎵 <b>Now Playing:</b> {title}",
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton("➕ Add to Playlist", callback_data=f"addpl_{song_hash}")],
                            [InlineKeyboardButton("⬅️ Main Menu", callback_data="main_menu")]
                        ]),
                        parse_mode=ParseMode.HTML
                    )
                # Always delete the original command and processing messages for a clean chat
                try:
                    await message.delete()
                except Exception as e:
                    logger.debug(f"Could not delete original message: {e}")
                try:
                    await process_msg.delete()
                except Exception as e:
                    logger.debug(f"Could not delete process_msg: {e}")
            except Exception as e:
                logger.error(f"Error in now_playing_callback: {e}")
        success, play_message = await play_in_voice_chat(user, chat_id, stream_url_to_use, title, now_playing_callback)
        # Always ensure queue_worker is running if there are items in the queue and nothing is playing
        try:
            from player.voice_chat import queue_workers, queue_worker, active_chats
            from asyncio import create_task
            if chat_id in active_chats:
                if (not active_chats[chat_id].get('current_track') or not active_chats[chat_id]['queue']) and (chat_id not in queue_workers or queue_workers[chat_id].done()):
                    queue_workers[chat_id] = create_task(queue_worker(chat_id))
        except Exception as e:
            import logging
            logging.getLogger('musicbot.main').warning(f'Could not ensure queue_worker after /play: {e}')
        if success:
            # The now_playing_callback is already called inside play_in_voice_chat
            # No need to call it again here to avoid duplicate messages
            try:
                await message.delete()
            except Exception as e:
                logger.debug(f"Could not delete original message: {e}")
            try:
                await process_msg.delete()
            except Exception as e:
                logger.debug(f"Could not delete process_msg: {e}")
        else:
            await process_msg.edit(f"❌ {play_message}")
            if not os.path.exists(songs_dir):
                os.makedirs(songs_dir)
            # Use a safe filename
            import re
            safe_title = re.sub(r'[^\w\-_\. ]', '_', title)
            audio_file = os.path.join(songs_dir, f"{safe_title}.mp3")

            # Set up yt-dlp options
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': audio_file,
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([stream_url])

            # Send the audio file
            await process_msg.edit(f"⬆️ Uploading: **{title}**")

            # Create caption with song info
            caption = f"🎵 **Title:** {title}\n"
            if duration:
                minutes, seconds = divmod(duration, 60)
                caption += f"⏱️ **Duration:** {minutes}:{seconds:02d}\n"

            # Send as audio file with thumbnail
            await message.reply_audio(
                audio_file,
                caption=caption,
                title=title,
                thumb=thumbnail,
                duration=duration
            )

            # (Do not delete the file, keep for playlists)
            await process_msg.delete()
        
    except Exception as e:
        await process_msg.edit(f"❌ Error: {str(e)}")

@bot.on_message(filters.command("search"))
async def search_command(_, message: Message):
    """Handle the /search command"""
    if len(message.command) < 2:
        await message.reply("❌ Please provide a search query after /search")
        return

    # Get the search query
    query = message.text.split(None, 1)[1]

    # Send processing message
    process_msg = await message.reply("🔍 Searching...")

    try:
        # Search for videos
        ydl_opts = {
            'format': 'bestaudio/best',
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'default_search': 'ytsearch5',  # Search for 5 videos
            # Uncomment and modify the line below if you've installed FFmpeg but it's not in PATH
            # 'ffmpeg_location': 'C:/ffmpeg/bin/ffmpeg.exe',
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            result = ydl.extract_info(f"ytsearch5:{query}", download=False)

        if 'entries' not in result or not result['entries']:
            await process_msg.edit("❌ No results found")
            return

        # Create a list of search results
        search_results = "🔍 **Search Results:**\n\n"
        buttons = []

        for i, entry in enumerate(result['entries'], 1):
            title = entry.get('title', 'Unknown Title')
            video_id = entry.get('id', '')
            url = f"https://www.youtube.com/watch?v={video_id}"

            search_results += f"{i}. [{title}]({url})\n"
            buttons.append([InlineKeyboardButton(f"{i}. {title[:30]}...", callback_data=f"play_{video_id}")])

        # Add a keyboard with the search results
        reply_markup = InlineKeyboardMarkup(buttons)

        await process_msg.edit(
            search_results,
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )

    except Exception as e:
        await process_msg.edit(f"❌ Error: {str(e)}")

@bot.on_message(filters.command("about"))
async def about_command(_, message: Message):
    """Handle the /about command with inline button"""
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("🔙 Main Menu", callback_data="main_menu")]
    ])
    await message.reply(
        "ℹ️ **About Telegram Music Bot**\n\n"
        "This bot allows you to download and play music from YouTube directly in Telegram!\n\n"
        "**Features:**\n"
        "• Download songs from YouTube\n"
        "• Play music in voice chats\n"
        "• Queue system for multiple songs\n"
        "• Search YouTube for songs\n\n"
        "**Commands:**\n"
        "• /play <song name or URL> - Join VC and play a song\n"
        "• /search <song name> - Search for a song on YouTube\n"
        "• /queue - Show current song queue\n"
        "• /skip - Skip to the next song in queue\n"
        "• /remove <position> - Remove a song from the queue\n"
        "• /clear - Clear the song queue\n"
        "• /pause - Pause the current playback\n"
        "• /resume - Resume the paused playback\n"
        "• /leave - Leave the voice chat\n",
        reply_markup=keyboard
    )

@bot.on_message(filters.command("vcnote"))
async def vcnote_command(_, message: Message):
    """Handle the /vcnote command"""
    await message.reply(
        "🎵 **Voice Chat Playback:**\n\n"
        "The bot can now automatically join voice chats and play music! Here's how it works:\n\n"
        "1. Just use `/play <song name or URL>` to play a song directly\n"
        "2. Multiple songs will be automatically queued\n"
        "3. Songs will play one after another automatically\n\n"
        "**Queue Commands:**\n"
        "• /queue - Show current song queue\n"
        "• /skip - Skip to the next song in queue\n"
        "• /remove <position> - Remove a song from the queue\n"
        "• /clear - Clear the song queue\n\n"
        "⚠️ **Important Requirements:**\n"
        "FFmpeg and FFprobe are required for voice chat functionality.\n\n"
        "If you see an error about FFmpeg or FFprobe not being installed, simply run:\n"
        "```\n"
        "python download_ffmpeg.py\n"
        "```\n\n"
        "This will automatically download and set up FFmpeg for you!\n"
    )

@bot.on_message(filters.command("joinvc"))
async def joinvc_command(_, message: Message):
    """Handle the /joinvc command"""
    chat_id = message.chat.id
    from player import join_voice_chat

    success, result = await join_voice_chat(client, chat_id)
    await message.reply(result)

@bot.on_message(filters.command("playvc"))
async def playvc_command(client, message: Message):
    """Handle the /playvc command"""
    if len(message.command) < 2:
        await message.reply("❌ Please provide a song name or URL after /playvc")
        return

    # Get the song query
    query = message.text.split(None, 1)[1]
    chat_id = message.chat.id

    # Send processing message
    process_msg = await message.reply("🔄 Processing...")

    try:
        # Get stream info
        stream_info = stream_from_url(query)
        if not stream_info:
            await process_msg.edit("❌ Could not find audio source")
            return

        title = stream_info['title']
        stream_url = stream_info['stream_url']

        # Play in voice chat (this will automatically join if not already in)
        from player import play_in_voice_chat
        success, result = await play_in_voice_chat(client, chat_id, stream_url, title)

        if success:
            await process_msg.edit(f"🎵 **Now playing in voice chat:** {title}")
        else:
            await process_msg.edit(f"❌ Error: {result}")

    except Exception as e:
        await process_msg.edit(f"❌ Error: {str(e)}")

@bot.on_message(filters.command("pausevc"))
async def pausevc_command(client, message: Message):
    """Handle the /pausevc command"""
    chat_id = message.chat.id
    from player import pause_voice_chat

    success, result = await pause_voice_chat(client, chat_id)
    await message.reply(result)

@bot.on_message(filters.command("resumevc"))
async def resumevc_command(client, message: Message):
    """Handle the /resumevc command"""
    chat_id = message.chat.id
    from player import resume_voice_chat

    success, result = await resume_voice_chat(client, chat_id)
    await message.reply(result)

@bot.on_message(filters.command("leavevc"))
async def leavevc_command(client, message: Message):
    """Handle the /leavevc command"""
    chat_id = message.chat.id
    from player import leave_voice_chat

    success, result = await leave_voice_chat(client, chat_id)
    await message.reply(result)

# Universal callback query handler for menu, playlist, and hash-based actions
@bot.on_callback_query()
async def universal_callback_handler(client, callback_query):
    data = callback_query.data
    message = callback_query.message
    chat_id = message.chat.id
    user_id = callback_query.from_user.id
    
    # Main menu
    if data == "main_menu":
        # Create a more organized and visually appealing menu
        menu_buttons = [
            # Music playback controls
            [
                InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play "),
                InlineKeyboardButton("⏯️ Controls", callback_data="playback_controls")
            ],
            # Queue management
            [
                InlineKeyboardButton("🎶 Queue", callback_data="show_queue"),
                InlineKeyboardButton("🔄 Skip", callback_data="skip_song")
            ],
            # Playlist management
            [
                InlineKeyboardButton("📂 My Playlists", callback_data="show_playlists"),
                InlineKeyboardButton("➕ New Playlist", callback_data="create_playlist_prompt")
            ],
            # Additional options
            [
                InlineKeyboardButton("⚙️ Settings", callback_data="settings"),
                InlineKeyboardButton("ℹ️ Help", callback_data="about")
            ]
        ]
        
        # Check if we're in a group chat
        is_group = message.chat.type in ["group", "supergroup"]
        
        # Add voice chat controls if in a group
        if is_group:
            voice_chat_buttons = [
                InlineKeyboardButton("🎤 Join Voice", callback_data="join_voice_chat"),
                InlineKeyboardButton("👋 Leave Voice", callback_data="leave_voice_chat")
            ]
            menu_buttons.insert(1, voice_chat_buttons)
        
        # Send a more informative welcome message
        welcome_text = (
            "🎧 <b>TG Music Bot</b> 🎧\n\n"
            "Play music in voice chats, create playlists, and more!\n\n"
            "<b>Quick Commands:</b>\n"
            "• /play <song> - Play a song\n"
            "• /queue - Show current queue\n"
            "• /pause - Pause playback\n"
            "• /resume - Resume playback\n"
            "• /skip - Skip to next song\n\n"
            "Use the buttons below for easy access to all features:"
        )
        
        await message.edit_text(
            welcome_text,
            reply_markup=InlineKeyboardMarkup(menu_buttons),
            parse_mode=ParseMode.HTML
        )
        await callback_query.answer()
    elif data == "show_queue":
        from player.voice_chat import get_queue, active_chats
        queue = await get_queue(chat_id)
        
        # Get current playing track if any
        current_track = None
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            current_track = active_chats[chat_id]['current_track']
        
        # Build queue text with current track and queue
        if current_track:
            # Calculate elapsed time
            import time
            elapsed = time.time() - current_track.get('start_time', 0)
            duration = current_track.get('duration', 0)
            
            # Format time
            if duration > 0:
                elapsed_mins, elapsed_secs = divmod(int(elapsed), 60)
                total_mins, total_secs = divmod(int(duration), 60)
                time_str = f" ({elapsed_mins}:{elapsed_secs:02d}/{total_mins}:{total_secs:02d})"
            else:
                time_str = ""
                
            queue_text = f"🎵 <b>Now Playing:</b>\n• {current_track['title']}{time_str}\n\n"
            
            if queue:
                queue_text += "<b>Up Next:</b>\n"
            else:
                queue_text += "<i>Queue is empty. Add more songs!</i>\n"
        else:
            queue_text = "🔄 <b>Nothing playing currently</b>\n\n"
            if queue:
                queue_text += "<b>Songs in Queue:</b>\n"
            else:
                queue_text += "<i>Queue is empty. Add songs with /play!</i>\n"
        
        # Add queue items
        for idx, song in enumerate(queue, 1):
            queue_text += f"{idx}. {song['title']}\n"
        
        # Create buttons for queue management
        buttons = []
        
        # Add buttons for each song in queue
        for idx, song in enumerate(queue, 1):
            title_short = song['title'][:25] + ("..." if len(song['title']) > 25 else "")
            buttons.append([
                InlineKeyboardButton(f"{idx}. {title_short}", callback_data=f"queue_info_{idx}"),
                InlineKeyboardButton("❌", callback_data=f"queue_remove_{idx}")
            ])
        
        # Add queue control buttons
        control_buttons = []
        if current_track:
            control_buttons = [
                InlineKeyboardButton("⏸️ Pause", callback_data="pause_playback"),
                InlineKeyboardButton("⏭️ Skip", callback_data="skip_song")
            ]
        
        if control_buttons:
            buttons.append(control_buttons)
            
        # Add navigation buttons
        buttons.append([
            InlineKeyboardButton("🔄 Refresh", callback_data="show_queue"),
            InlineKeyboardButton("⬅️ Back", callback_data="main_menu")
        ])
        
        await message.edit_text(queue_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
        await callback_query.answer()
    elif data == "show_playlists":
        try:
            # Get user's playlists
            from utils.playlist import list_playlists
            playlists = await list_playlists(user_id)
            
            playlists_text = "📂 <b>Your Playlists:</b>\n\n" if playlists else "📂 <b>Your Playlists</b>\n\n<i>You don't have any playlists yet.</i>"
            
            # Show playlist details
            for name in playlists:
                try:
                    # Try to get song count
                    from config import BASE_DIR
                    playlist_path = os.path.join(BASE_DIR, 'playlists', f"{user_id}_{name}.json")
                    if os.path.exists(playlist_path):
                        with open(playlist_path, 'r') as f:
                            playlist_data = json.load(f)
                            song_count = len(playlist_data.get("songs", []))
                            playlists_text += f"• <b>{name}</b> ({song_count} songs)\n"
                    else:
                        playlists_text += f"• <b>{name}</b>\n"
                except Exception as e:
                    logger.error(f"Error reading playlist {name}: {e}")
                    playlists_text += f"• <b>{name}</b>\n"
            
            # Create buttons for each playlist with multiple actions
            buttons = []
            for name in playlists:
                name_hash = hash(name) & 0xFFFFFFFF
                buttons.append([
                    InlineKeyboardButton(f"🎶 {name[:20]}" + ("..." if len(name) > 20 else ""), 
                                        callback_data=f"pl_view_{name_hash}"),
                    InlineKeyboardButton("▶️", callback_data=f"pl_play_{name_hash}"),
                    InlineKeyboardButton("❌", callback_data=f"pl_delete_{name_hash}")
                ])
            
            # Add create playlist button
            buttons.append([InlineKeyboardButton("➕ Create New Playlist", callback_data="create_playlist_prompt")])
            
            # Add back button
            buttons.append([InlineKeyboardButton("⬅️ Back", callback_data="main_menu")])
            
            await message.edit_text(playlists_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
            await callback_query.answer()
        except Exception as e:
            logger.error(f"Error showing playlists: {e}")
            await message.edit_text(
                f"❌ Error loading playlists: {str(e)}\n\nPlease try again.",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]]),
                parse_mode=ParseMode.HTML
            )
            await callback_query.answer("Error loading playlists")
        
    elif data == "create_playlist_prompt":
        # Show a prompt to create a new playlist
        text = (
            "📝 <b>Create a New Playlist</b>\n\n"
            "Please enter a name for your new playlist using the command:\n\n"
            "<code>/createplaylist playlist_name</code>\n\n"
            "For example: <code>/createplaylist My Favorites</code>"
        )
        buttons = [[InlineKeyboardButton("⬅️ Back", callback_data="show_playlists")]]
        await message.edit_text(text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
        await callback_query.answer()
        
    elif data.startswith("pl_view_"):
        try:
            # View playlist contents
            playlist_hash = int(data.split("_")[2])
            
            # Find the playlist name from hash
            from utils.playlist import list_playlists
            playlists = await list_playlists(user_id)
            playlist_name = None
            
            for name in playlists:
                if (hash(name) & 0xFFFFFFFF) == playlist_hash:
                    playlist_name = name
                    break
                    
            if not playlist_name:
                await callback_query.answer("❌ Playlist not found")
                return
                
            # Get playlist contents
            from utils.playlist import get_playlist
            playlist = await get_playlist(playlist_name, user_id)
            
            if not playlist:
                await callback_query.answer("❌ Could not load playlist")
                return
                
            # Display playlist contents
            songs = playlist.songs
            playlist_text = f"🎶 <b>Playlist: {playlist_name}</b>\n\n"
            
            if not songs:
                playlist_text += "<i>This playlist is empty. Add songs to it!</i>"
            else:
                for idx, song in enumerate(songs, 1):
                    playlist_text += f"{idx}. {song.get('title', 'Unknown')}\n"
                    
            # Create buttons for playlist actions
            buttons = []
            
            # Add play button if playlist has songs
            if songs:
                buttons.append([
                    InlineKeyboardButton("▶️ Play All", callback_data=f"pl_play_{playlist_hash}"),
                    InlineKeyboardButton("🔀 Shuffle", callback_data=f"pl_shuffle_{playlist_hash}")
                ])
                
            # Add back button
            buttons.append([InlineKeyboardButton("⬅️ Back to Playlists", callback_data="show_playlists")])
            
            await message.edit_text(playlist_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
            await callback_query.answer()
        except Exception as e:
            logger.error(f"Error viewing playlist: {e}")
            await message.edit_text(
                f"❌ Error viewing playlist: {str(e)}\n\nPlease try again.",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("⬅️ Back", callback_data="show_playlists")]]),
                parse_mode=ParseMode.HTML
            )
            await callback_query.answer("Error viewing playlist")
        
    elif data == "playback_controls":
        # Show playback controls
        from player.voice_chat import active_chats, is_playing
        
        is_currently_playing = await is_playing(chat_id)
        current_track = None
        
        if chat_id in active_chats and active_chats[chat_id].get('current_track'):
            current_track = active_chats[chat_id]['current_track']
            
        if current_track and is_currently_playing:
            # Calculate elapsed time
            import time
            elapsed = time.time() - current_track.get('start_time', 0)
            duration = current_track.get('duration', 0)
            
            # Format time
            if duration > 0:
                elapsed_mins, elapsed_secs = divmod(int(elapsed), 60)
                total_mins, total_secs = divmod(int(duration), 60)
                time_str = f"({elapsed_mins}:{elapsed_secs:02d}/{total_mins}:{total_secs:02d})"
            else:
                time_str = ""
                
            controls_text = (
                f"🎵 <b>Now Playing:</b> {current_track['title']}\n"
                f"⏱️ {time_str}\n\n"
                f"Use the controls below to manage playback:"
            )
            
            # Create playback control buttons
            buttons = [
                [
                    InlineKeyboardButton("⏮️ Prev", callback_data="prev_song"),
                    InlineKeyboardButton("⏸️ Pause", callback_data="pause_playback"),
                    InlineKeyboardButton("⏭️ Next", callback_data="skip_song")
                ],
                [
                    InlineKeyboardButton("🔄 Refresh", callback_data="playback_controls"),
                    InlineKeyboardButton("🎶 Queue", callback_data="show_queue")
                ],
                [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
            ]
        else:
            controls_text = (
                "⏹️ <b>Nothing Playing</b>\n\n"
                "There's no music playing right now. Use /play to start playing music!"
            )
            
            # Create simplified buttons when nothing is playing
            buttons = [
                [
                    InlineKeyboardButton("🎵 Play Music", switch_inline_query_current_chat="/play "),
                    InlineKeyboardButton("🎶 Queue", callback_data="show_queue")
                ],
                [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
            ]
            
        await message.edit_text(controls_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=ParseMode.HTML)
        await callback_query.answer()
        
    # Playback control actions
    elif data == "pause_playback":
        from player.voice_chat import pause_voice_chat
        success, result = await pause_voice_chat(client, chat_id)
        await callback_query.answer(result)
        # Refresh the playback controls
        await message.edit_text("🔄 Pausing playback...")
        await client.invoke_callback_data(message, "playback_controls")
        
    elif data == "resume_playback":
        from player.voice_chat import resume_voice_chat
        success, result = await resume_voice_chat(client, chat_id)
        await callback_query.answer(result)
        # Refresh the playback controls
        await message.edit_text("🔄 Resuming playback...")
        await client.invoke_callback_data(message, "playback_controls")
        
    elif data == "skip_song":
        from player.voice_chat import skip_current_song
        success, result = await skip_current_song(client, chat_id)
        await callback_query.answer(result)
        # Refresh the queue view
        await message.edit_text("🔄 Skipping to next song...")
        await client.invoke_callback_data(message, "show_queue")
        
    elif data == "join_voice_chat":
        from player.voice_chat import join_voice_chat
        success, result = await join_voice_chat(client, chat_id)
        await callback_query.answer(result)
        # Return to main menu
        await message.edit_text("🔄 Joining voice chat...")
        await client.invoke_callback_data(message, "main_menu")
        
    elif data == "leave_voice_chat":
        from player.voice_chat import leave_voice_chat
        success, result = await leave_voice_chat(client, chat_id)
        await callback_query.answer(result)
        # Return to main menu
        await message.edit_text("🔄 Leaving voice chat...")
        await client.invoke_callback_data(message, "main_menu")
        
    # Queue management
    elif data.startswith("queue_remove_"):
        try:
            position = int(data.split("_")[2])
            from player.voice_chat import remove_from_queue
            success, result = await remove_from_queue(chat_id, position)
            await callback_query.answer(result)
            # Refresh the queue view
            await message.edit_text("🔄 Updating queue...")
            await client.invoke_callback_data(message, "show_queue")
        except Exception as e:
            logger.error(f"Error removing from queue: {e}")
            await callback_query.answer("❌ Error removing song from queue")
    elif data.startswith("addpl_"):
        song_hash = int(data.split("_", 1)[1])
        song_title = song_hash_to_title.get(song_hash)
        if not song_title:
            await message.reply("❌ Could not resolve song title for Add to Playlist.")
            await callback_query.answer()
            return
        playlists_dir = os.path.join(os.path.dirname(__file__), 'playlists')
        playlists = [f[:-5] for f in os.listdir(playlists_dir) if f.endswith('.json')]
        if not playlists:
            await message.reply("ℹ️ No playlists found. Use /createplaylist <name> to create one.")
            await callback_query.answer()
            return
        buttons = [[InlineKeyboardButton(f"➕ {name}", callback_data=f"add_song_to_playlist:{name}:{song_hash}")] for name in playlists]
        buttons.append([InlineKeyboardButton("⬅️ Main Menu", callback_data="main_menu")])
        await message.reply(f"Select a playlist to add '{song_title}' to:", reply_markup=InlineKeyboardMarkup(buttons))
        await callback_query.answer()
    elif data.startswith("add_song_to_playlist:"):
        _, playlist_name, song_hash_str = data.split(":", 2)
        song_hash = int(song_hash_str)
        song_title = song_hash_to_title.get(song_hash)
        if not song_title:
            await message.reply("❌ Could not resolve song title for Add to Playlist.")
            await callback_query.answer()
            return
        playlists_dir = os.path.join(os.path.dirname(__file__), 'playlists')
        playlist_path = os.path.join(playlists_dir, f"{playlist_name}.json")
        if not os.path.exists(playlist_path):
            await message.reply(f"❌ Playlist '{playlist_name}' does not exist.")
            await callback_query.answer()
            return
        songs_dir = os.path.join(os.path.dirname(__file__), 'downloaded_songs')
        import re
        safe_title = re.sub(r'[^\w\-_\. ]', '_', song_title)
        song_file = os.path.join(songs_dir, f"{safe_title}.mp3")
        song_file_mp3mp3 = song_file + ".mp3"
        # Allow adding to playlist if either file exists or if it is being downloaded (optimistic add)
        if not (os.path.exists(song_file) or os.path.exists(song_file_mp3mp3)):
            # Check if a background download is in progress (file might be created soon)
            import time
            wait_time = 0
            while wait_time < 5 and not (os.path.exists(song_file) or os.path.exists(song_file_mp3mp3)):
                time.sleep(1)
                wait_time += 1
            if not (os.path.exists(song_file) or os.path.exists(song_file_mp3mp3)):
                await message.reply(f"❌ Song '{song_title}' not found in downloaded songs. Try playing it first to cache it.")
                await callback_query.answer()
                return
        # Use the file that exists for playlist storage
        playlist_song_file = song_file if os.path.exists(song_file) else song_file_mp3mp3
        with open(playlist_path, 'r+', encoding='utf-8') as f:
            playlist = json.load(f)
            if song_file in playlist or song_file_mp3mp3 in playlist:
                await message.reply(f"ℹ️ Song already in playlist '{playlist_name}'.")
                await callback_query.answer()
                return
            playlist.append(playlist_song_file)
            f.seek(0)
            json.dump(playlist, f, indent=2)
            f.truncate()
        await message.reply(f"✅ Added '{song_title}' to playlist '{playlist_name}'.")
        # Ensure queue_worker is running if a song is added to the queue and nothing is playing
        try:
            from player.voice_chat import queue_workers, queue_worker, active_chats
            from asyncio import create_task
            if chat_id in active_chats:
                # If nothing is playing and queue is not empty, start the worker
                if (not active_chats[chat_id].get('current_track') or not active_chats[chat_id]['queue']) and (chat_id not in queue_workers or queue_workers[chat_id].done()):
                    queue_workers[chat_id] = create_task(queue_worker(chat_id))
        except Exception as e:
            import logging
            logging.getLogger('musicbot.main').warning(f'Could not ensure queue_worker after playlist add: {e}')
        await callback_query.answer()
    elif data.startswith("pl_"):
        # Play playlist or show playlist menu (implement as needed)
        await message.reply("Playlist menu feature coming soon!")
        await callback_query.answer()
    else:
        await callback_query.answer("Unknown action.")

# Callback query handler for search results
@bot.on_callback_query(filters.regex(r"^play_(.+)"))
async def play_callback(_, callback_query):
    """Handle play callback from search results"""
    video_id = callback_query.data.split("_")[1]
    url = f"https://www.youtube.com/watch?v={video_id}"

    # Answer the callback query
    await callback_query.answer("Processing your request...")

    # Send a new message with the /play command
    await play_command(None, Message(
        id=0,
        chat=callback_query.message.chat,
        text=f"/play {url}",
        command=["play", url],
        from_user=callback_query.from_user
    ))

# Initialize PyTgCalls
from player.voice_chat import initialize_tgcalls, FFMPEG_PATH, FFPROBE_PATH
import os

# Set FFmpeg paths as environment variables
if os.path.exists(FFMPEG_PATH):
    os.environ["FFMPEG_BINARY"] = FFMPEG_PATH
if os.path.exists(FFPROBE_PATH):
    os.environ["FFPROBE_BINARY"] = FFPROBE_PATH

# Main function
async def main():
    """Start the bot and user clients"""
    print("Starting Telegram Music Bot...")
    
    # Register playlist handlers
    register_playlist_handlers(bot)
    
    await bot.start()
    await user.start()
    
    # Initialize PyTgCalls with the user client
    pytgcalls = await initialize_tgcalls(user)
    
    print("Bot is running. Press Ctrl+C to stop.")
    print("✅ Voice chat functionality is enabled!")
    await idle()

if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(main())
