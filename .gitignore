# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Telegram session files
*.session
*.session-journal

# Logs
*.log

# Local configuration

# Downloaded songs and audio cache
/downloaded_songs/
/audio_cache/

# Playlists (user-generated data)
/playlists/

# FFmpeg binaries (local, not source)
/ffmpeg/

# Telegram session files
*.session
*.session-journal

# Sensitive configuration
.env
config.py
config.json

# Bot session and cache
music_bot_session_*
user_session_string_*

# Pyrogram/Hydrogram session artifacts
*.session*

# Ignore any backup or temp files
*~
*.bak
*.tmp

# Ignore logs
*.log

# Ignore OS-specific files
.DS_Store
Thumbs.db

.env
