"""
Download and extract FFmpeg for Windows
"""

import os
import sys
import zipfile
import tempfile
import subprocess
import shutil
from urllib.request import urlretrieve
from urllib.error import URLError

# FFmpeg download URL (Windows 64-bit static build)
FFMPEG_URL = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
FFMPEG_DIR = "ffmpeg"

def download_progress(count, block_size, total_size):
    """Show download progress"""
    percent = int(count * block_size * 100 / total_size)
    sys.stdout.write(f"\rDownloading FFmpeg... {percent}%")
    sys.stdout.flush()

def download_ffmpeg():
    """Download FFmpeg"""
    if os.path.exists(FFMPEG_DIR) and os.path.exists(os.path.join(FFMPEG_DIR, "bin", "ffmpeg.exe")):
        print(f"FFmpeg already downloaded in {FFMPEG_DIR}")
        return True
    
    print("Downloading FFmpeg...")
    temp_zip = os.path.join(tempfile.gettempdir(), "ffmpeg.zip")
    
    try:
        urlretrieve(FFMPEG_URL, temp_zip, download_progress)
        print("\nDownload complete!")
    except URLError as e:
        print(f"\nError downloading FFmpeg: {e}")
        return False
    
    print("Extracting FFmpeg...")
    try:
        with zipfile.ZipFile(temp_zip, 'r') as zip_ref:
            # Get the name of the top-level directory in the zip
            top_dir = zip_ref.namelist()[0].split('/')[0]
            zip_ref.extractall(tempfile.gettempdir())
        
        # Move the extracted directory to the desired location
        extracted_path = os.path.join(tempfile.gettempdir(), top_dir)
        if os.path.exists(FFMPEG_DIR):
            shutil.rmtree(FFMPEG_DIR)
        shutil.move(extracted_path, FFMPEG_DIR)
        
        print(f"FFmpeg extracted to {FFMPEG_DIR}")
        return True
    except Exception as e:
        print(f"Error extracting FFmpeg: {e}")
        return False
    finally:
        # Clean up the temporary zip file
        if os.path.exists(temp_zip):
            os.remove(temp_zip)

def check_ffmpeg():
    """Check if FFmpeg is available"""
    ffmpeg_path = os.path.join(FFMPEG_DIR, "bin", "ffmpeg.exe")
    ffprobe_path = os.path.join(FFMPEG_DIR, "bin", "ffprobe.exe")
    
    if os.path.exists(ffmpeg_path) and os.path.exists(ffprobe_path):
        print("FFmpeg and FFprobe are available!")
        
        # Test FFmpeg
        try:
            subprocess.run(
                [ffmpeg_path, "-version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
            print("FFmpeg is working correctly!")
        except subprocess.SubprocessError:
            print("FFmpeg is installed but not working correctly.")
            return False
        
        # Test FFprobe
        try:
            subprocess.run(
                [ffprobe_path, "-version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
            print("FFprobe is working correctly!")
        except subprocess.SubprocessError:
            print("FFprobe is installed but not working correctly.")
            return False
        
        return True
    else:
        print("FFmpeg and/or FFprobe are not available.")
        return False

if __name__ == "__main__":
    if download_ffmpeg():
        check_ffmpeg()
    else:
        print("Failed to download FFmpeg.")
