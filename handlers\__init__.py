"""
Handlers module for the Telegram Music Bot
Provides better separation of concerns by organizing functionality into modules
"""

from .player_controls import (
    create_enhanced_now_playing_message,
    handle_pause_playback,
    handle_resume_playback,
    handle_skip_song,
    handle_previous_song,
    handle_refresh_now_playing,
    update_playback_controls,
    update_now_playing_message
)

from .menu_manager import (
    create_main_menu,
    create_playback_controls_menu,
    create_queue_menu,
    create_settings_menu,
    create_help_menu
)

from .advanced_features import (
    handle_clear_queue,
    handle_queue_remove,
    handle_queue_info
)

__all__ = [
    # Player controls
    'create_enhanced_now_playing_message',
    'handle_pause_playback',
    'handle_resume_playback',
    'handle_skip_song',
    'handle_previous_song',
    'handle_refresh_now_playing',
    'update_playback_controls',
    'update_now_playing_message',

    # Menu management
    'create_main_menu',
    'create_playback_controls_menu',
    'create_queue_menu',
    'create_settings_menu',
    'create_help_menu',

    # Essential features only
    'handle_clear_queue',
    'handle_queue_remove',
    'handle_queue_info'
]
